import { Tool } from './context';

// Simple cursor icons with better browser compatibility
const CURSOR_ICONS = {
  select: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M2 2L8 8L6 14L8 12L14 6L8 8Z" fill="white" stroke="black" stroke-width="1"/></svg>')}`,

  brush: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="3" fill="white" stroke="black" stroke-width="1"/></svg>')}`,

  rectangle: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="3" width="10" height="7" fill="none" stroke="white" stroke-width="2"/><circle cx="8" cy="8" r="1" fill="white"/></svg>')}`,

  circle: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="5" fill="none" stroke="white" stroke-width="2"/><circle cx="8" cy="8" r="1" fill="white"/></svg>')}`,

  text: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3 3H13M8 3V13M6 13H10" stroke="white" stroke-width="2" fill="none"/></svg>')}`,

  eraser: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><rect x="4" y="4" width="8" height="8" fill="pink" stroke="white" stroke-width="1" rx="1"/></svg>')}`,

  hand: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M8 2V8M6 4V8M10 4V8M4 6V10C4 12 6 14 8 14C10 14 12 12 12 10V6" stroke="white" stroke-width="1.5" fill="none"/></svg>')}`,

  image: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><rect x="2" y="2" width="12" height="12" fill="none" stroke="white" stroke-width="1.5"/><circle cx="5" cy="5" r="1" fill="white"/><path d="M14 10L10 6L2 14" stroke="white" stroke-width="1.5"/></svg>')}`,

  selection: `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><rect x="4" y="4" width="8" height="8" fill="none" stroke="white" stroke-width="1.5" stroke-dasharray="2 2"/><circle cx="8" cy="8" r="1" fill="white"/></svg>')}`
};

// Generate CSS cursor string with custom icon
export const getToolCursor = (tool: Tool, isPanning: boolean = false): string => {
  if (isPanning) {
    return 'grabbing';
  }

  // For tools with custom icons, use the SVG cursor
  if (CURSOR_ICONS[tool]) {
    return `url("${CURSOR_ICONS[tool]}") 8 8, auto`;
  }

  // Fallback to standard cursors with better differentiation
  switch (tool) {
    case 'hand':
      return 'grab';
    case 'text':
      return 'text';
    case 'brush':
    case 'eraser':
      return 'crosshair';
    case 'rectangle':
    case 'circle':
    case 'selection':
      return 'crosshair';
    case 'image':
      return 'copy';
    case 'select':
    default:
      return 'default';
  }
};

// Keyboard shortcuts mapping
export const KEYBOARD_SHORTCUTS: Record<string, Tool> = {
  'KeyA': 'select',      // Ctrl + A
  'KeyB': 'brush',       // Ctrl + B  
  'KeyR': 'rectangle',   // Ctrl + R
  'KeyC': 'circle',      // Ctrl + C
  'KeyT': 'text',        // Ctrl + T
  'KeyE': 'eraser',      // Ctrl + E
  'KeyH': 'hand',        // Ctrl + H
  'KeyI': 'image',       // Ctrl + I
  'KeyS': 'selection',   // Ctrl + S
};

// Get tool name for display in tooltips
export const getToolDisplayName = (tool: Tool): string => {
  const names: Record<Tool, string> = {
    select: 'Select',
    rectangle: 'Rectangle', 
    circle: 'Circle',
    text: 'Text',
    brush: 'Brush',
    eraser: 'Eraser',
    image: 'Image',
    hand: 'Hand',
    selection: 'Selection'
  };
  return names[tool] || tool;
};

// Get keyboard shortcut for tool (for tooltip display)
export const getToolShortcut = (tool: Tool): string => {
  const shortcut = Object.entries(KEYBOARD_SHORTCUTS).find(([_, t]) => t === tool)?.[0];
  if (shortcut) {
    const key = shortcut.replace('Key', '');
    return `Ctrl + ${key}`;
  }
  return '';
};
