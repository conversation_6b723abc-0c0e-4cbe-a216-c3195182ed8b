{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\nimport * as fabric from 'fabric';\r\n\r\nexport type Tool = 'select' | 'rectangle' | 'circle' | 'text' | 'brush' | 'eraser' | 'image' | 'hand' | 'selection';\r\n\r\nexport type Unit = 'px' | 'cm' | 'in' | 'ft';\r\n\r\nexport interface BoardSize {\r\n  width: number;\r\n  height: number;\r\n  unit: Unit;\r\n}\r\n\r\n// Define the shape of the context state\r\ninterface ImageEditorContextType {\r\n  canvas: fabric.Canvas | null;\r\n  setCanvas: (canvas: fabric.Canvas | null) => void;\r\n  activeTool: Tool;\r\n  setActiveTool: (tool: Tool) => void;\r\n  brushSize: number;\r\n  setBrushSize: (size: number) => void;\r\n  brushColor: string;\r\n  setBrushColor: (color: string) => void;\r\n  fillColor: string;\r\n  setFillColor: (color: string) => void;\r\n  strokeColor: string;\r\n  setStrokeColor: (color: string) => void;\r\n  strokeWidth: number;\r\n  setStrokeWidth: (width: number) => void;\r\n  isDrawing: boolean;\r\n  setIsDrawing: (drawing: boolean) => void;\r\n  // Canvas viewport state\r\n  zoom: number;\r\n  setZoom: (zoom: number) => void;\r\n  panX: number;\r\n  setPanX: (x: number) => void;\r\n  panY: number;\r\n  setPanY: (y: number) => void;\r\n  // Toolbar position\r\n  toolbarPosition: { x: number; y: number };\r\n  setToolbarPosition: (position: { x: number; y: number }) => void;\r\n  // Board size state\r\n  boardSize: BoardSize | null;\r\n  setBoardSize: (size: BoardSize | null) => void;\r\n  showBoardSizeModal: boolean;\r\n  setShowBoardSizeModal: (show: boolean) => void;\r\n  // Helper function to show board size modal\r\n  showBoardSizeSettings: () => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ImageEditorContext = createContext<ImageEditorContextType | null>(null);\r\n\r\n// Create a provider component\r\ninterface ImageEditorProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {\r\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\r\n  const [activeTool, setActiveTool] = useState<Tool>('select');\r\n  const [brushSize, setBrushSize] = useState(5);\r\n  const [brushColor, setBrushColor] = useState('#000000');\r\n  const [fillColor, setFillColor] = useState('#ff0000');\r\n  const [strokeColor, setStrokeColor] = useState('#000000');\r\n  const [strokeWidth, setStrokeWidth] = useState(2);\r\n  const [isDrawing, setIsDrawing] = useState(false);\r\n\r\n  // Canvas viewport state\r\n  const [zoom, setZoom] = useState(1);\r\n  const [panX, setPanX] = useState(0);\r\n  const [panY, setPanY] = useState(0);\r\n\r\n  // Toolbar position\r\n  const [toolbarPosition, setToolbarPosition] = useState({ x: 500, y: 60 });\r\n\r\n  // Board size state\r\n  const [boardSize, setBoardSize] = useState<BoardSize | null>(null);\r\n  const [showBoardSizeModal, setShowBoardSizeModal] = useState(true); // Show modal by default when no board size is set\r\n\r\n  // Custom setBoardSize that also handles modal visibility\r\n  const handleSetBoardSize = (size: BoardSize | null) => {\r\n    setBoardSize(size);\r\n    if (size) {\r\n      setShowBoardSizeModal(false); // Hide modal when board size is set\r\n    }\r\n  };\r\n\r\n  // Helper function to show board size settings\r\n  const showBoardSizeSettings = () => {\r\n    setShowBoardSizeModal(true);\r\n  };\r\n\r\n  return (\r\n    <ImageEditorContext.Provider value={{\r\n      canvas,\r\n      setCanvas,\r\n      activeTool,\r\n      setActiveTool,\r\n      brushSize,\r\n      setBrushSize,\r\n      brushColor,\r\n      setBrushColor,\r\n      fillColor,\r\n      setFillColor,\r\n      strokeColor,\r\n      setStrokeColor,\r\n      strokeWidth,\r\n      setStrokeWidth,\r\n      isDrawing,\r\n      setIsDrawing,\r\n      zoom,\r\n      setZoom,\r\n      panX,\r\n      setPanX,\r\n      panY,\r\n      setPanY,\r\n      toolbarPosition,\r\n      setToolbarPosition,\r\n      boardSize,\r\n      setBoardSize: handleSetBoardSize,\r\n      showBoardSizeModal,\r\n      setShowBoardSizeModal,\r\n      showBoardSizeSettings\r\n    }}>\r\n      {children}\r\n    </ImageEditorContext.Provider>\r\n  );\r\n};\r\n\r\n// Create a custom hook to use the context\r\nexport const useImageEditor = () => {\r\n  const context = useContext(ImageEditorContext);\r\n  if (!context) {\r\n    throw new Error('useImageEditor must be used within an ImageEditorProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoDA,0CAA0C;AAC1C,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAOjE,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA4B;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAG;IAEvE,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,kDAAkD;IAEtH,yDAAyD;IACzD,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,IAAI,MAAM;YACR,sBAAsB,QAAQ,oCAAoC;QACpE;IACF;IAEA,8CAA8C;IAC9C,MAAM,wBAAwB;QAC5B,sBAAsB;IACxB;IAEA,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;YACd;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAGO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,qMAAA,CAAA,UAAa,CAC3B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Canvas.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useCallback, useState } from 'react';\r\nimport * as fabric from 'fabric';\r\nimport { useImageEditor } from './context';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\n\r\nconst Canvas = () => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\r\n  const [isPanning, setIsPanning] = useState(false);\r\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\r\n  const [selectionRect, setSelectionRect] = useState<fabric.Rect | null>(null);\r\n\r\n  const {\r\n    canvas,\r\n    setCanvas,\r\n    activeTool,\r\n    brushSize,\r\n    brushColor,\r\n    fillColor,\r\n    strokeColor,\r\n    strokeWidth,\r\n    zoom,\r\n    setZoom,\r\n    panX,\r\n    setPanX,\r\n    panY,\r\n    setPanY,\r\n    boardSize\r\n  } = useImageEditor();\r\n\r\n  // Handle window resize to make canvas fullscreen\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setDimensions({\r\n        width: window.innerWidth,\r\n        height: window.innerHeight\r\n      });\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Handle wheel events for zooming and panning\r\n  const handleWheel = useCallback((e: WheelEvent) => {\r\n    e.preventDefault();\r\n    if (!canvas) return;\r\n\r\n    if (e.ctrlKey || e.metaKey) {\r\n      // Zoom towards the mouse pointer\r\n      const delta = e.deltaY > 0 ? 0.9 : 1.1;\r\n      const newZoom = Math.max(0.01, Math.min(100, canvas.getZoom() * delta));\r\n      const point = new fabric.Point(e.offsetX, e.offsetY);\r\n      canvas.zoomToPoint(point, newZoom);\r\n    } else {\r\n      // Pan with the wheel\r\n      const delta = new fabric.Point(-e.deltaX, -e.deltaY);\r\n      canvas.relativePan(delta);\r\n    }\r\n\r\n    // Sync state with canvas viewport\r\n    const vpt = canvas.viewportTransform;\r\n    if (vpt) {\r\n      setZoom(vpt[0]);\r\n      setPanX(vpt[4]);\r\n      setPanY(vpt[5]);\r\n    }\r\n    canvas.renderAll();\r\n  }, [canvas, setZoom, setPanX, setPanY]);\r\n\r\n  // Handle panning with space + drag or hand tool\r\n  const handleMouseDown = useCallback((e: MouseEvent) => {\r\n    if (activeTool === 'hand' || e.button === 1) {\r\n      setIsPanning(true);\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n      e.preventDefault();\r\n    }\r\n  }, [activeTool]);\r\n\r\n  const handleMouseMove = useCallback((e: MouseEvent) => {\r\n    if (isPanning && canvas) {\r\n      const deltaX = e.clientX - lastPanPoint.x;\r\n      const deltaY = e.clientY - lastPanPoint.y;\r\n\r\n      canvas.relativePan(new fabric.Point(deltaX, deltaY));\r\n      \r\n      // Sync state with canvas viewport\r\n      const vpt = canvas.viewportTransform;\r\n      if (vpt) {\r\n        setPanX(vpt[4]);\r\n        setPanY(vpt[5]);\r\n      }\r\n\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n    }\r\n  }, [isPanning, lastPanPoint, canvas, setPanX, setPanY]);\r\n\r\n  const handleMouseUp = useCallback(() => {\r\n    setIsPanning(false);\r\n  }, []);\r\n\r\n  // Handle keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      // Space key for temporary hand tool\r\n      if (e.code === 'Space' && !e.repeat) {\r\n        e.preventDefault();\r\n        document.body.style.cursor = 'grab';\r\n      }\r\n\r\n      // Zoom shortcuts\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '0') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          // Reset zoom to 100% and center the view\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, 1);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom In (Ctrl/Cmd + Plus/Equal)\r\n      if ((e.ctrlKey || e.metaKey) && (e.key === '=' || e.key === '+')) {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          const newZoom = Math.min(100, canvas.getZoom() * 1.25);\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, newZoom);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom Out (Ctrl/Cmd + Minus)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '-') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          const newZoom = Math.max(0.01, canvas.getZoom() * 0.8);\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, newZoom);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Fit to Screen (Ctrl/Cmd + 1)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '1') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          // This can be customized to fit objects, for now, it resets to 100%\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, 1);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleKeyUp = (e: KeyboardEvent) => {\r\n      if (e.code === 'Space') {\r\n        document.body.style.cursor = 'default';\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    window.addEventListener('keyup', handleKeyUp);\r\n\r\n    return () => {\r\n      window.removeEventListener('keydown', handleKeyDown);\r\n      window.removeEventListener('keyup', handleKeyUp);\r\n    };\r\n  }, [canvas, setZoom]);\r\n\r\n  // Update drawing mode when tool changes\r\n  useEffect(() => {\r\n    if (!canvas) return;\r\n\r\n    // Store the active tool and properties on the canvas for event handlers\r\n    (canvas as any).activeTool = activeTool;\r\n    (canvas as any).fillColor = fillColor;\r\n    (canvas as any).strokeColor = strokeColor;\r\n    (canvas as any).strokeWidth = strokeWidth;\r\n\r\n    if (activeTool === 'brush') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        canvas.freeDrawingBrush.color = brushColor;\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    } else if (activeTool === 'selection') {\r\n      canvas.isDrawingMode = false;\r\n      canvas.selection = true;\r\n      canvas.defaultCursor = 'crosshair';\r\n    }\r\n    else if (activeTool === 'eraser') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        // Set eraser mode by using destination-out composite operation\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'destination-out';\r\n      }\r\n    } else {\r\n      canvas.isDrawingMode = false;\r\n      // Reset composite operation\r\n      if (canvas.freeDrawingBrush) {\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    }\r\n  }, [canvas, activeTool, brushSize, brushColor, fillColor, strokeColor, strokeWidth]);\r\n\r\n  const setupCanvasEvents = useCallback((canvas: fabric.Canvas) => {\r\n    // Handle mouse events for shape creation\r\n    let isDown = false;\r\n    let origX = 0;\r\n    let origY = 0;\r\n    let shape: fabric.Object | null = null;\r\n\r\n    const onMouseDown = (o: any) => {\r\n      // Get current tool from the canvas data or use a closure\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n      if (currentTool === 'select' || currentTool === 'brush' || currentTool === 'eraser' || currentTool === 'hand') return;\r\n\r\n      isDown = true;\r\n      const pointer = canvas.getPointer(o.e);\r\n      origX = pointer.x;\r\n      origY = pointer.y;\r\n\r\n      if (currentTool === 'selection') {\r\n        canvas.selection = false;\r\n        const rect = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: 'rgba(0,0,0,0.3)',\r\n          stroke: 'black',\r\n          strokeDashArray: [5, 5],\r\n          selectable: true,\r\n        });\r\n        setSelectionRect(rect);\r\n        canvas.add(rect);\r\n        return; // Prevent creating other shapes\r\n      }\r\n\r\n      if (currentTool === 'rectangle') {\r\n        shape = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      } else if (currentTool === 'circle') {\r\n        shape = new fabric.Circle({\r\n          left: origX,\r\n          top: origY,\r\n          radius: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      }\r\n\r\n      if (shape) {\r\n        canvas.add(shape);\r\n      }\r\n    };\r\n\r\n    const onMouseMove = (o: any) => {\r\n      if (!isDown) return;\r\n\r\n      const pointer = canvas.getPointer(o.e);\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n\r\n      if (currentTool === 'selection' && selectionRect) {\r\n        const rect = selectionRect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (currentTool === 'rectangle' && shape) {\r\n        const rect = shape as fabric.Rect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (currentTool === 'circle') {\r\n        const circle = shape as fabric.Circle;\r\n        const radius = Math.sqrt(Math.pow(pointer.x - origX, 2) + Math.pow(pointer.y - origY, 2)) / 2;\r\n        circle.set({ radius });\r\n      }\r\n\r\n      canvas.renderAll();\r\n    };\r\n\r\n    const onMouseUp = () => {\r\n      isDown = false;\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n\r\n      if (currentTool === 'selection' && selectionRect) {\r\n        cropToSelection(selectionRect);\r\n        canvas.remove(selectionRect);\r\n        setSelectionRect(null);\r\n        canvas.selection = true; // Re-enable selection\r\n      }\r\n\r\n      if (shape) {\r\n        shape.set({ selectable: true });\r\n        shape = null;\r\n      }\r\n      isDown = false;\r\n    };\r\n\r\n    canvas.on('mouse:down', onMouseDown);\r\n    canvas.on('mouse:move', onMouseMove);\r\n    canvas.on('mouse:up', onMouseUp);\r\n\r\n    return () => {\r\n      canvas.off('mouse:down', onMouseDown);\r\n      canvas.off('mouse:move', onMouseMove);\r\n      canvas.off('mouse:up', onMouseUp);\r\n    };\r\n  }, [activeTool, fillColor, strokeColor, strokeWidth, selectionRect]);\r\n\r\n  const cropToSelection = (rect: fabric.Rect) => {\r\n    if (!canvas || !rect.width || !rect.height) return;\r\n\r\n    const cropped = new Image();\r\n    cropped.src = canvas.toDataURL({\r\n      left: rect.left,\r\n      top: rect.top,\r\n      width: rect.width,\r\n      height: rect.height,\r\n      multiplier: 1,\r\n    });\r\n\r\n    cropped.onload = () => {\r\n      canvas.clear();\r\n      const image = new fabric.FabricImage(cropped);\r\n      canvas.setWidth(rect.width || 0);\r\n      canvas.setHeight(rect.height || 0);\r\n      canvas.add(image);\r\n      canvas.renderAll();\r\n    };\r\n  };\r\n\r\n  // Initialize canvas when dimensions are available\r\n  useEffect(() => {\r\n    if (canvasRef.current && dimensions.width > 0 && dimensions.height > 0) {\r\n      const fabricCanvas = new fabric.Canvas(canvasRef.current, {\r\n        width: dimensions.width,\r\n        height: dimensions.height,\r\n        backgroundColor: 'transparent',\r\n      });\r\n\r\n      // Enable viewport transform for panning and zooming\r\n      fabricCanvas.selection = true;\r\n\r\n      setCanvas(fabricCanvas);\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        fabricCanvas.dispose();\r\n      };\r\n    }\r\n  }, [dimensions, setCanvas]);\r\n\r\n  // Setup events once when canvas is created\r\n  useEffect(() => {\r\n    if (canvas) {\r\n      const cleanup = setupCanvasEvents(canvas);\r\n      return cleanup;\r\n    }\r\n  }, [canvas, setupCanvasEvents]);\r\n\r\n  // Create board rectangle when boardSize is set\r\n  useEffect(() => {\r\n    if (canvas && boardSize) {\r\n      // Convert board size to pixels\r\n      const UNIT_TO_PX: Record<string, number> = {\r\n        px: 1,\r\n        cm: 37.8, // 1 cm ≈ 37.8 pixels at 96 DPI\r\n        in: 96,   // 1 inch = 96 pixels at 96 DPI\r\n        ft: 1152  // 1 foot = 12 inches = 1152 pixels at 96 DPI\r\n      };\r\n\r\n      const boardWidthPx = boardSize.width * UNIT_TO_PX[boardSize.unit];\r\n      const boardHeightPx = boardSize.height * UNIT_TO_PX[boardSize.unit];\r\n\r\n      // Clear existing board if any\r\n      const existingBoard = canvas.getObjects().find(obj => (obj as any).isBoard);\r\n      if (existingBoard) {\r\n        canvas.remove(existingBoard);\r\n      }\r\n\r\n      // Create board rectangle centered on canvas\r\n      const centerX = dimensions.width / 2;\r\n      const centerY = dimensions.height / 2;\r\n\r\n      const boardRect = new fabric.Rect({\r\n        left: centerX - boardWidthPx / 2,\r\n        top: centerY - boardHeightPx / 2,\r\n        width: boardWidthPx,\r\n        height: boardHeightPx,\r\n        fill: 'white',\r\n        stroke: '#e5e7eb',\r\n        strokeWidth: 2,\r\n        selectable: false,\r\n        evented: false,\r\n        excludeFromExport: false,\r\n      });\r\n\r\n      // Mark as board for identification\r\n      (boardRect as any).isBoard = true;\r\n\r\n      // Add board to canvas (send to back)\r\n      canvas.add(boardRect);\r\n      // Move board to the back (index 0)\r\n      canvas.moveObjectTo(boardRect, 0);\r\n      canvas.renderAll();\r\n    }\r\n  }, [canvas, boardSize, dimensions]);\r\n\r\n  // Add event listeners for mouse and wheel events\r\n  useEffect(() => {\r\n    const container = containerRef.current;\r\n    if (!container) return;\r\n\r\n    container.addEventListener('wheel', handleWheel, { passive: false });\r\n    container.addEventListener('mousedown', handleMouseDown);\r\n    container.addEventListener('mousemove', handleMouseMove);\r\n    container.addEventListener('mouseup', handleMouseUp);\r\n\r\n    return () => {\r\n      container.removeEventListener('wheel', handleWheel);\r\n      container.removeEventListener('mousedown', handleMouseDown);\r\n      container.removeEventListener('mousemove', handleMouseMove);\r\n      container.removeEventListener('mouseup', handleMouseUp);\r\n    };\r\n  }, [handleWheel, handleMouseDown, handleMouseMove, handleMouseUp]);\r\n\r\n  const getCursor = () => {\r\n    if (isPanning) return 'grabbing';\r\n    switch (activeTool) {\r\n      case 'hand':\r\n        return 'grab';\r\n      case 'rectangle':\r\n      case 'circle':\r\n      case 'selection':\r\n      case 'brush':\r\n      case 'eraser':\r\n        return 'crosshair';\r\n      case 'text':\r\n        return 'text';\r\n      case 'select':\r\n      default:\r\n        return 'default';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"fixed inset-0 overflow-hidden\"\r\n      style={{\r\n        background: `\r\n          radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n          radial-gradient(circle at 75% 75%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n          linear-gradient(135deg, #667eea 0%, #764ba2 100%)\r\n        `,\r\n        cursor: getCursor()\r\n      }}\r\n    >\r\n      {/* Infinite grid background */}\r\n      <div\r\n        className=\"absolute inset-0\"\r\n        style={{\r\n          backgroundImage: `\r\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\r\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\r\n          `,\r\n          backgroundSize: `20px 20px`,\r\n          backgroundPosition: `${panX}px ${panY}px`,\r\n        }}\r\n      />\r\n\r\n      {/* Canvas */}\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"absolute top-0 left-0 z-10\"\r\n        style={{\r\n          display: 'block',\r\n          width: dimensions.width,\r\n          height: dimensions.height\r\n        }}\r\n      />\r\n\r\n      {/* Zoom indicator with interactive slider */}\r\n      <div className=\"fixed bottom-4 right-4 z-50\">\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <button className=\"bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm hover:bg-black/60 transition-colors cursor-pointer\">\r\n              {Math.round(zoom * 100)}%\r\n            </button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-80 bg-black/80 backdrop-blur-md border-white/20\" side=\"top\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-white\">Zoom Level</label>\r\n                <span className=\"text-sm text-white/70\">{Math.round(zoom * 100)}%</span>\r\n              </div>\r\n              <Slider\r\n                value={[zoom * 100]}\r\n                onValueChange={(value) => {\r\n                  const newZoom = value[0] / 100;\r\n                  setZoom(newZoom);\r\n                  if (canvas) {\r\n                    canvas.setZoom(newZoom);\r\n                    canvas.renderAll();\r\n                  }\r\n                }}\r\n                max={10000}\r\n                min={1}\r\n                step={1}\r\n                className=\"flex-1\"\r\n              />\r\n              {/* Zoom Presets */}\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex gap-1 flex-wrap justify-center\">\r\n                  {[25, 50, 75, 100, 125, 150, 200, 400, 800, 1600].map((percent) => (\r\n                    <button\r\n                      key={percent}\r\n                      onClick={() => {\r\n                        const newZoom = percent / 100;\r\n                        setZoom(newZoom);\r\n                        if (canvas) {\r\n                          canvas.setZoom(newZoom);\r\n                          canvas.renderAll();\r\n                        }\r\n                      }}\r\n                      className={`px-2 py-1 text-xs rounded transition-colors ${\r\n                        Math.round(zoom * 100) === percent\r\n                          ? 'bg-white/30 text-white font-medium'\r\n                          : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-white'\r\n                      }`}\r\n                    >\r\n                      {percent}%\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Range Labels */}\r\n                <div className=\"flex items-center justify-between text-xs text-white/50\">\r\n                  <span>1%</span>\r\n                  <span className=\"text-white/70\">Professional Zoom Range</span>\r\n                  <span>10000%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Canvas;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AASA,MAAM,SAAS;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEvE,MAAM,EACJ,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,EACV,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEjB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ;QAEb,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;YAC1B,iCAAiC;YACjC,MAAM,QAAQ,EAAE,MAAM,GAAG,IAAI,MAAM;YACnC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO,OAAO,KAAK;YAChE,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAY,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;YACnD,OAAO,WAAW,CAAC,OAAO;QAC5B,OAAO;YACL,qBAAqB;YACrB,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAY,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;YACnD,OAAO,WAAW,CAAC;QACrB;QAEA,kCAAkC;QAClC,MAAM,MAAM,OAAO,iBAAiB;QACpC,IAAI,KAAK;YACP,QAAQ,GAAG,CAAC,EAAE;YACd,QAAQ,GAAG,CAAC,EAAE;YACd,QAAQ,GAAG,CAAC,EAAE;QAChB;QACA,OAAO,SAAS;IAClB,GAAG;QAAC;QAAQ;QAAS;QAAS;KAAQ;IAEtC,gDAAgD;IAChD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,eAAe,UAAU,EAAE,MAAM,KAAK,GAAG;YAC3C,aAAa;YACb,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC7C,EAAE,cAAc;QAClB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,aAAa,QAAQ;YACvB,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;YACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;YAEzC,OAAO,WAAW,CAAC,IAAI,+IAAA,CAAA,QAAY,CAAC,QAAQ;YAE5C,kCAAkC;YAClC,MAAM,MAAM,OAAO,iBAAiB;YACpC,IAAI,KAAK;gBACP,QAAQ,GAAG,CAAC,EAAE;gBACd,QAAQ,GAAG,CAAC,EAAE;YAChB;YAEA,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAC/C;IACF,GAAG;QAAC;QAAW;QAAc;QAAQ;QAAS;KAAQ;IAEtD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,aAAa;IACf,GAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,oCAAoC;YACpC,IAAI,EAAE,IAAI,KAAK,WAAW,CAAC,EAAE,MAAM,EAAE;gBACnC,EAAE,cAAc;gBAChB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B;YAEA,iBAAiB;YACjB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,IAAI,QAAQ;oBACV,yCAAyC;oBACzC,MAAM,SAAS,IAAI,+IAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;oBAC5E,OAAO,WAAW,CAAC,QAAQ;oBAC3B,MAAM,MAAM,OAAO,iBAAiB;oBACpC,IAAI,KAAK;wBACP,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;oBAChB;oBACA,OAAO,SAAS;gBAClB;YACF;YAEA,kCAAkC;YAClC,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,GAAG,GAAG;gBAChE,EAAE,cAAc;gBAChB,IAAI,QAAQ;oBACV,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,OAAO,OAAO,KAAK;oBACjD,MAAM,SAAS,IAAI,+IAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;oBAC5E,OAAO,WAAW,CAAC,QAAQ;oBAC3B,MAAM,MAAM,OAAO,iBAAiB;oBACpC,IAAI,KAAK;wBACP,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;oBAChB;oBACA,OAAO,SAAS;gBAClB;YACF;YAEA,8BAA8B;YAC9B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,IAAI,QAAQ;oBACV,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,OAAO,OAAO,KAAK;oBAClD,MAAM,SAAS,IAAI,+IAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;oBAC5E,OAAO,WAAW,CAAC,QAAQ;oBAC3B,MAAM,MAAM,OAAO,iBAAiB;oBACpC,IAAI,KAAK;wBACP,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;oBAChB;oBACA,OAAO,SAAS;gBAClB;YACF;YAEA,+BAA+B;YAC/B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,IAAI,QAAQ;oBACV,oEAAoE;oBACpE,MAAM,SAAS,IAAI,+IAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;oBAC5E,OAAO,WAAW,CAAC,QAAQ;oBAC3B,MAAM,MAAM,OAAO,iBAAiB;oBACpC,IAAI,KAAK;wBACP,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;wBACd,QAAQ,GAAG,CAAC,EAAE;oBAChB;oBACA,OAAO,SAAS;gBAClB;YACF;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,EAAE,IAAI,KAAK,SAAS;gBACtB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,SAAS;QAEjC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,SAAS;QACtC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,wEAAwE;QACvE,OAAe,UAAU,GAAG;QAC5B,OAAe,SAAS,GAAG;QAC3B,OAAe,WAAW,GAAG;QAC7B,OAAe,WAAW,GAAG;QAE9B,IAAI,eAAe,SAAS;YAC1B,OAAO,aAAa,GAAG;YACvB,IAAI,OAAO,gBAAgB,EAAE;gBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAChC,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAC/B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF,OAAO,IAAI,eAAe,aAAa;YACrC,OAAO,aAAa,GAAG;YACvB,OAAO,SAAS,GAAG;YACnB,OAAO,aAAa,GAAG;QACzB,OACK,IAAI,eAAe,UAAU;YAChC,OAAO,aAAa,GAAG;YACvB,IAAI,OAAO,gBAAgB,EAAE;gBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAChC,+DAA+D;gBAC9D,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF,OAAO;YACL,OAAO,aAAa,GAAG;YACvB,4BAA4B;YAC5B,IAAI,OAAO,gBAAgB,EAAE;gBAC1B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF;IACF,GAAG;QAAC;QAAQ;QAAY;QAAW;QAAY;QAAW;QAAa;KAAY;IAEnF,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,yCAAyC;QACzC,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAI,QAAQ;QACZ,IAAI,QAA8B;QAElC,MAAM,cAAc,CAAC;YACnB,yDAAyD;YACzD,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;YAClD,IAAI,gBAAgB,YAAY,gBAAgB,WAAW,gBAAgB,YAAY,gBAAgB,QAAQ;YAE/G,SAAS;YACT,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YACrC,QAAQ,QAAQ,CAAC;YACjB,QAAQ,QAAQ,CAAC;YAEjB,IAAI,gBAAgB,aAAa;gBAC/B,OAAO,SAAS,GAAG;gBACnB,MAAM,OAAO,IAAI,+IAAA,CAAA,OAAW,CAAC;oBAC3B,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,iBAAiB;wBAAC;wBAAG;qBAAE;oBACvB,YAAY;gBACd;gBACA,iBAAiB;gBACjB,OAAO,GAAG,CAAC;gBACX,QAAQ,gCAAgC;YAC1C;YAEA,IAAI,gBAAgB,aAAa;gBAC/B,QAAQ,IAAI,+IAAA,CAAA,OAAW,CAAC;oBACtB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM,AAAC,OAAe,SAAS,IAAI;oBACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;oBACvC,aAAa,AAAC,OAAe,WAAW,IAAI;oBAC5C,YAAY;gBACd;YACF,OAAO,IAAI,gBAAgB,UAAU;gBACnC,QAAQ,IAAI,+IAAA,CAAA,SAAa,CAAC;oBACxB,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,MAAM,AAAC,OAAe,SAAS,IAAI;oBACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;oBACvC,aAAa,AAAC,OAAe,WAAW,IAAI;oBAC5C,YAAY;gBACd;YACF;YAEA,IAAI,OAAO;gBACT,OAAO,GAAG,CAAC;YACb;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,QAAQ;YAEb,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YACrC,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;YAElD,IAAI,gBAAgB,eAAe,eAAe;gBAChD,MAAM,OAAO;gBACb,KAAK,GAAG,CAAC;oBACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;oBAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;gBAC/B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,MAAM,QAAQ,CAAC;oBAAC;gBAC7B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,KAAK,QAAQ,CAAC;oBAAC;gBAC5B;YACF,OAAO,IAAI,gBAAgB,eAAe,OAAO;gBAC/C,MAAM,OAAO;gBACb,KAAK,GAAG,CAAC;oBACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;oBAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;gBAC/B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,MAAM,QAAQ,CAAC;oBAAC;gBAC7B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,KAAK,QAAQ,CAAC;oBAAC;gBAC5B;YACF,OAAO,IAAI,gBAAgB,UAAU;gBACnC,MAAM,SAAS;gBACf,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,MAAM;gBAC5F,OAAO,GAAG,CAAC;oBAAE;gBAAO;YACtB;YAEA,OAAO,SAAS;QAClB;QAEA,MAAM,YAAY;YAChB,SAAS;YACT,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;YAElD,IAAI,gBAAgB,eAAe,eAAe;gBAChD,gBAAgB;gBAChB,OAAO,MAAM,CAAC;gBACd,iBAAiB;gBACjB,OAAO,SAAS,GAAG,MAAM,sBAAsB;YACjD;YAEA,IAAI,OAAO;gBACT,MAAM,GAAG,CAAC;oBAAE,YAAY;gBAAK;gBAC7B,QAAQ;YACV;YACA,SAAS;QACX;QAEA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,YAAY;QAEtB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,YAAY;QACzB;IACF,GAAG;QAAC;QAAY;QAAW;QAAa;QAAa;KAAc;IAEnE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,EAAE;QAE5C,MAAM,UAAU,IAAI;QACpB,QAAQ,GAAG,GAAG,OAAO,SAAS,CAAC;YAC7B,MAAM,KAAK,IAAI;YACf,KAAK,KAAK,GAAG;YACb,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,YAAY;QACd;QAEA,QAAQ,MAAM,GAAG;YACf,OAAO,KAAK;YACZ,MAAM,QAAQ,IAAI,+IAAA,CAAA,cAAkB,CAAC;YACrC,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI;YAC9B,OAAO,SAAS,CAAC,KAAK,MAAM,IAAI;YAChC,OAAO,GAAG,CAAC;YACX,OAAO,SAAS;QAClB;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,IAAI,WAAW,KAAK,GAAG,KAAK,WAAW,MAAM,GAAG,GAAG;YACtE,MAAM,eAAe,IAAI,+IAAA,CAAA,SAAa,CAAC,UAAU,OAAO,EAAE;gBACxD,OAAO,WAAW,KAAK;gBACvB,QAAQ,WAAW,MAAM;gBACzB,iBAAiB;YACnB;YAEA,oDAAoD;YACpD,aAAa,SAAS,GAAG;YAEzB,UAAU;YAEV,sBAAsB;YACtB,OAAO;gBACL,aAAa,OAAO;YACtB;QACF;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,MAAM,UAAU,kBAAkB;YAClC,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;KAAkB;IAE9B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,+BAA+B;YAC/B,MAAM,aAAqC;gBACzC,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI,KAAM,6CAA6C;YACzD;YAEA,MAAM,eAAe,UAAU,KAAK,GAAG,UAAU,CAAC,UAAU,IAAI,CAAC;YACjE,MAAM,gBAAgB,UAAU,MAAM,GAAG,UAAU,CAAC,UAAU,IAAI,CAAC;YAEnE,8BAA8B;YAC9B,MAAM,gBAAgB,OAAO,UAAU,GAAG,IAAI,CAAC,CAAA,MAAO,AAAC,IAAY,OAAO;YAC1E,IAAI,eAAe;gBACjB,OAAO,MAAM,CAAC;YAChB;YAEA,4CAA4C;YAC5C,MAAM,UAAU,WAAW,KAAK,GAAG;YACnC,MAAM,UAAU,WAAW,MAAM,GAAG;YAEpC,MAAM,YAAY,IAAI,+IAAA,CAAA,OAAW,CAAC;gBAChC,MAAM,UAAU,eAAe;gBAC/B,KAAK,UAAU,gBAAgB;gBAC/B,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,mBAAmB;YACrB;YAEA,mCAAmC;YAClC,UAAkB,OAAO,GAAG;YAE7B,qCAAqC;YACrC,OAAO,GAAG,CAAC;YACX,mCAAmC;YACnC,OAAO,YAAY,CAAC,WAAW;YAC/B,OAAO,SAAS;QAClB;IACF,GAAG;QAAC;QAAQ;QAAW;KAAW;IAElC,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,UAAU,gBAAgB,CAAC,SAAS,aAAa;YAAE,SAAS;QAAM;QAClE,UAAU,gBAAgB,CAAC,aAAa;QACxC,UAAU,gBAAgB,CAAC,aAAa;QACxC,UAAU,gBAAgB,CAAC,WAAW;QAEtC,OAAO;YACL,UAAU,mBAAmB,CAAC,SAAS;YACvC,UAAU,mBAAmB,CAAC,aAAa;YAC3C,UAAU,mBAAmB,CAAC,aAAa;YAC3C,UAAU,mBAAmB,CAAC,WAAW;QAC3C;IACF,GAAG;QAAC;QAAa;QAAiB;QAAiB;KAAc;IAEjE,MAAM,YAAY;QAChB,IAAI,WAAW,OAAO;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,YAAY,CAAC;;;;QAIb,CAAC;YACD,QAAQ;QACV;;0BAGA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;UAGlB,CAAC;oBACD,gBAAgB,CAAC,SAAS,CAAC;oBAC3B,oBAAoB,GAAG,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC;gBAC3C;;;;;;0BAIF,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,SAAS;oBACT,OAAO,WAAW,KAAK;oBACvB,QAAQ,WAAW,MAAM;gBAC3B;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAO,WAAU;;oCACf,KAAK,KAAK,CAAC,OAAO;oCAAK;;;;;;;;;;;;sCAG5B,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAoD,MAAK;sCACjF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDAAK,WAAU;;oDAAyB,KAAK,KAAK,CAAC,OAAO;oDAAK;;;;;;;;;;;;;kDAElE,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;4CAAC,OAAO;yCAAI;wCACnB,eAAe,CAAC;4CACd,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG;4CAC3B,QAAQ;4CACR,IAAI,QAAQ;gDACV,OAAO,OAAO,CAAC;gDACf,OAAO,SAAS;4CAClB;wCACF;wCACA,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAI;oDAAI;oDAAI;oDAAK;oDAAK;oDAAK;oDAAK;oDAAK;oDAAK;iDAAK,CAAC,GAAG,CAAC,CAAC,wBACrD,8OAAC;wDAEC,SAAS;4DACP,MAAM,UAAU,UAAU;4DAC1B,QAAQ;4DACR,IAAI,QAAQ;gEACV,OAAO,OAAO,CAAC;gEACf,OAAO,SAAS;4DAClB;wDACF;wDACA,WAAW,CAAC,4CAA4C,EACtD,KAAK,KAAK,CAAC,OAAO,SAAS,UACvB,uCACA,gEACJ;;4DAED;4DAAQ;;uDAfJ;;;;;;;;;;0DAqBX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;uCAEe", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-9 px-2 min-w-9\",\n        sm: \"h-8 px-1.5 min-w-8\",\n        lg: \"h-10 px-2.5 min-w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Toggle({\n  className,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <TogglePrimitive.Root\n      data-slot=\"toggle\"\n      className={cn(toggleVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Toggle, toggleVariants }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ijBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,GAAG,OAEgC;IACnC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useImageEditor, Tool } from './context';\r\nimport * as fabric from 'fabric';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Toggle } from '@/components/ui/toggle';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport {\r\n  MousePointer2,\r\n  Square,\r\n  Circle,\r\n  Type,\r\n  Brush,\r\n  Eraser,\r\n  Image,\r\n  Download,\r\n  Trash2,\r\n  Copy,\r\n  Layers,\r\n  Hand,\r\n  Move,\r\n  Crop,\r\n  Settings\r\n} from 'lucide-react';\r\nimport React, { useRef, useState } from 'react';\r\n\r\nconst Toolbar = () => {\r\n  const {\r\n    canvas,\r\n    activeTool,\r\n    setActiveTool,\r\n    brushSize,\r\n    setBrushSize,\r\n    brushColor,\r\n    setBrushColor,\r\n    fillColor,\r\n    setFillColor,\r\n    strokeColor,\r\n    setStrokeColor,\r\n    strokeWidth,\r\n    setStrokeWidth,\r\n    toolbarPosition,\r\n    setToolbarPosition,\r\n    showBoardSizeSettings\r\n  } = useImageEditor();\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });\r\n\r\n  const tools: { id: Tool; icon: React.ReactNode; label: string }[] = [\r\n    { id: 'select', icon: <MousePointer2 size={18} />, label: 'Select' },\r\n    { id: 'hand', icon: <Hand size={18} />, label: 'Hand' },\r\n    { id: 'selection', icon: <Crop size={18} />, label: 'Select Area' },\r\n    { id: 'rectangle', icon: <Square size={18} />, label: 'Rectangle' },\r\n    { id: 'circle', icon: <Circle size={18} />, label: 'Circle' },\r\n    { id: 'text', icon: <Type size={18} />, label: 'Text' },\r\n    { id: 'brush', icon: <Brush size={18} />, label: 'Brush' },\r\n    { id: 'eraser', icon: <Eraser size={18} />, label: 'Eraser' },\r\n  ];\r\n\r\n  const handleToolSelect = (tool: Tool) => {\r\n    setActiveTool(tool);\r\n\r\n    if (tool === 'text' && canvas) {\r\n      const text = new fabric.IText('Click to edit', {\r\n        left: 100,\r\n        top: 100,\r\n        fontFamily: 'Arial',\r\n        fontSize: 20,\r\n        fill: fillColor,\r\n      });\r\n      canvas.add(text);\r\n      canvas.setActiveObject(text);\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file && canvas) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const imgUrl = e.target?.result as string;\r\n        fabric.FabricImage.fromURL(imgUrl).then((img: any) => {\r\n          img.set({\r\n            left: 50,\r\n            top: 50,\r\n            scaleX: 0.5,\r\n            scaleY: 0.5,\r\n          });\r\n          canvas.add(img);\r\n          canvas.renderAll();\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    if (canvas) {\r\n      const dataURL = canvas.toDataURL({\r\n        format: 'png',\r\n        quality: 1,\r\n        multiplier: 1,\r\n      });\r\n      const link = document.createElement('a');\r\n      link.download = 'design.png';\r\n      link.href = dataURL;\r\n      link.click();\r\n    }\r\n  };\r\n\r\n  const handleClear = () => {\r\n    if (canvas) {\r\n      canvas.clear();\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    if (canvas) {\r\n      const activeObject = canvas.getActiveObject();\r\n      if (activeObject) {\r\n        // Simple duplication by creating a new object with same properties\r\n        const objData = activeObject.toObject();\r\n        const cloned = new (activeObject.constructor as any)(objData);\r\n        cloned.set({\r\n          left: (activeObject.left || 0) + 10,\r\n          top: (activeObject.top || 0) + 10,\r\n        });\r\n        canvas.add(cloned);\r\n        canvas.setActiveObject(cloned);\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  const deleteSelected = () => {\r\n    if (canvas) {\r\n      const activeObject = canvas.getActiveObject();\r\n      if (activeObject) {\r\n        if (activeObject.type === 'activeSelection') {\r\n          (activeObject as fabric.ActiveSelection).forEachObject((obj) => {\r\n            canvas.remove(obj);\r\n          });\r\n        } else {\r\n          canvas.remove(activeObject);\r\n        }\r\n        canvas.discardActiveObject();\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle toolbar dragging\r\n  const handleMouseDown = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {\r\n      setIsDragging(true);\r\n      setDragOffset({\r\n        x: e.clientX - toolbarPosition.x,\r\n        y: e.clientY - toolbarPosition.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseMove = (e: MouseEvent) => {\r\n    if (isDragging) {\r\n      setToolbarPosition({\r\n        x: e.clientX - dragOffset.x,\r\n        y: e.clientY - dragOffset.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n  };\r\n\r\n  // Add global mouse event listeners for dragging\r\n  React.useEffect(() => {\r\n    if (isDragging) {\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n      document.addEventListener('mouseup', handleMouseUp);\r\n      return () => {\r\n        document.removeEventListener('mousemove', handleMouseMove);\r\n        document.removeEventListener('mouseup', handleMouseUp);\r\n      };\r\n    }\r\n  }, [isDragging, dragOffset]);\r\n\r\n  // Add global keydown event listener for deleting objects\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      if (e.key === 'Delete' || e.key === 'Backspace') {\r\n        deleteSelected();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [canvas]);\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div\r\n        className=\"fixed z-50 flex flex-wrap items-center gap-2 p-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg cursor-move select-none\"\r\n        style={{\r\n          left: toolbarPosition.x,\r\n          top: toolbarPosition.y,\r\n          maxWidth: '90vw'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n      >\r\n        {/* Drag Handle */}\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <div className=\"drag-handle flex items-center justify-center w-6 h-6 text-white/50 hover:text-white/80 cursor-grab active:cursor-grabbing\">\r\n              <Move size={14} />\r\n            </div>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Drag to move toolbar</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n        {/* Tool Selection */}\r\n        <div className=\"flex items-center flex-wrap gap-1 bg-white/5 rounded-lg p-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n          {tools.map((tool) => (\r\n            <Tooltip key={tool.id}>\r\n              <TooltipTrigger asChild>\r\n                <Toggle\r\n                  pressed={activeTool === tool.id}\r\n                  onPressedChange={() => handleToolSelect(tool.id)}\r\n                  className={`h-10 w-10 hover:bg-white/10 ${activeTool === tool.id ? 'bg-white/20 text-white' : ''}`}\r\n                  aria-label={tool.label}\r\n                >\r\n                  {tool.icon}\r\n                </Toggle>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{tool.label}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          ))}\r\n        </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Color Controls */}\r\n      <div className=\"flex flex-wrap items-center gap-2\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border border-white/30\"\r\n                    style={{ backgroundColor: fillColor }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Fill Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Fill Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={fillColor}\r\n                onChange={(e) => setFillColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border-2\"\r\n                    style={{ borderColor: strokeColor, backgroundColor: 'transparent' }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Stroke Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Stroke Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={strokeColor}\r\n                onChange={(e) => setStrokeColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Size Controls */}\r\n      {(activeTool === 'brush' || activeTool === 'eraser') && (\r\n        <>\r\n          <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n            <Brush size={16} className=\"text-white/70\" />\r\n            <Slider\r\n              value={[brushSize]}\r\n              onValueChange={(value) => setBrushSize(value[0])}\r\n              max={50}\r\n              min={1}\r\n              step={1}\r\n              className=\"flex-1\"\r\n            />\r\n            <span className=\"text-xs text-white/70 w-6\">{brushSize}</span>\r\n          </div>\r\n\r\n          {activeTool === 'brush' && (\r\n            <Popover>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <PopoverTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                    >\r\n                      <div\r\n                        className=\"w-full h-full rounded border border-white/30\"\r\n                        style={{ backgroundColor: brushColor }}\r\n                      />\r\n                    </Button>\r\n                  </PopoverTrigger>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Brush Color</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n              <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n                <div className=\"space-y-3\">\r\n                  <label className=\"text-sm font-medium text-white\">Brush Color</label>\r\n                  <input\r\n                    type=\"color\"\r\n                    value={brushColor}\r\n                    onChange={(e) => setBrushColor(e.target.value)}\r\n                    className=\"w-full h-10 rounded border-0\"\r\n                  />\r\n                </div>\r\n              </PopoverContent>\r\n            </Popover>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {(activeTool === 'rectangle' || activeTool === 'circle') && (\r\n        <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n          <div className=\"w-4 h-0.5 bg-white/70 rounded\" />\r\n          <Slider\r\n            value={[strokeWidth]}\r\n            onValueChange={(value) => setStrokeWidth(value[0])}\r\n            max={20}\r\n            min={0}\r\n            step={1}\r\n            className=\"flex-1\"\r\n          />\r\n          <span className=\"text-xs text-white/70 w-6\">{strokeWidth}</span>\r\n        </div>\r\n      )}\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex flex-wrap items-center gap-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => fileInputRef.current?.click()}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              {/* <Upload size={18} /> */}\r\n              <Image size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Upload Image</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={showBoardSizeSettings}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Settings size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Board Settings</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleDownload}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Download size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Download Design</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopy}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Copy size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Duplicate Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={deleteSelected}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Trash2 size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Delete Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleClear}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-red-400 hover:text-red-300\"\r\n            >\r\n              <Layers size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Clear Canvas</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </div>\r\n    </div>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default Toolbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AA3BA;;;;;;;;;;;;AA6BA,MAAM,UAAU;IACd,MAAM,EACJ,MAAM,EACN,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,WAAW,EACX,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,qBAAqB,EACtB,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE1D,MAAM,QAA8D;QAClE;YAAE,IAAI;YAAU,oBAAM,8OAAC,4NAAA,CAAA,gBAAa;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QACnE;YAAE,IAAI;YAAQ,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAa,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAc;QAClE;YAAE,IAAI;YAAa,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAY;QAClE;YAAE,IAAI;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QAC5D;YAAE,IAAI;YAAQ,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAS,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAQ;QACzD;YAAE,IAAI;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;KAC7D;IAED,MAAM,mBAAmB,CAAC;QACxB,cAAc;QAEd,IAAI,SAAS,UAAU,QAAQ;YAC7B,MAAM,OAAO,IAAI,+IAAA,CAAA,QAAY,CAAC,iBAAiB;gBAC7C,MAAM;gBACN,KAAK;gBACL,YAAY;gBACZ,UAAU;gBACV,MAAM;YACR;YACA,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,QAAQ,QAAQ;YAClB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,+IAAA,CAAA,cAAkB,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACvC,IAAI,GAAG,CAAC;wBACN,MAAM;wBACN,KAAK;wBACL,QAAQ;wBACR,QAAQ;oBACV;oBACA,OAAO,GAAG,CAAC;oBACX,OAAO,SAAS;gBAClB;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,UAAU,OAAO,SAAS,CAAC;gBAC/B,QAAQ;gBACR,SAAS;gBACT,YAAY;YACd;YACA,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG;YAChB,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,QAAQ;YACV,OAAO,KAAK;YACZ,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,MAAM,eAAe,OAAO,eAAe;YAC3C,IAAI,cAAc;gBAChB,mEAAmE;gBACnE,MAAM,UAAU,aAAa,QAAQ;gBACrC,MAAM,SAAS,IAAK,aAAa,WAAW,CAAS;gBACrD,OAAO,GAAG,CAAC;oBACT,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;oBACjC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;gBACjC;gBACA,OAAO,GAAG,CAAC;gBACX,OAAO,eAAe,CAAC;gBACvB,OAAO,SAAS;YAClB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,eAAe,OAAO,eAAe;YAC3C,IAAI,cAAc;gBAChB,IAAI,aAAa,IAAI,KAAK,mBAAmB;oBAC1C,aAAwC,aAAa,CAAC,CAAC;wBACtD,OAAO,MAAM,CAAC;oBAChB;gBACF,OAAO;oBACL,OAAO,MAAM,CAAC;gBAChB;gBACA,OAAO,mBAAmB;gBAC1B,OAAO,SAAS;YAClB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,AAAC,EAAE,MAAM,CAAiB,SAAS,CAAC,QAAQ,CAAC,gBAAgB;YAC/F,cAAc;YACd,cAAc;gBACZ,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;gBAChC,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;YAClC;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,mBAAmB;gBACjB,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;gBAC3B,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;YAC7B;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,gDAAgD;IAChD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,WAAW;YAC1C;QACF;IACF,GAAG;QAAC;QAAY;KAAW;IAE3B,yDAAyD;IACzD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,aAAa;gBAC/C;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBACL,MAAM,gBAAgB,CAAC;gBACvB,KAAK,gBAAgB,CAAC;gBACtB,UAAU;YACZ;YACA,aAAa;;8BAGb,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;sCAGhB,8OAAC,4HAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;8BAIP,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAA8D,aAAa,CAAC,IAAM,EAAE,eAAe;8BAC/G,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,eAAe,KAAK,EAAE;wCAC/B,iBAAiB,IAAM,iBAAiB,KAAK,EAAE;wCAC/C,WAAW,CAAC,4BAA4B,EAAE,eAAe,KAAK,EAAE,GAAG,2BAA2B,IAAI;wCAClG,cAAY,KAAK,KAAK;kDAErB,KAAK,IAAI;;;;;;;;;;;8CAGd,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAG,KAAK,KAAK;;;;;;;;;;;;2BAZJ,KAAK,EAAE;;;;;;;;;;8BAkB3B,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAAoC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCACtF,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAU;;;;;;;;;;;;;;;;;;;;;sDAK5C,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,aAAa;4DAAa,iBAAiB;wDAAc;;;;;;;;;;;;;;;;;;;;;sDAK1E,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;gBAG3C,CAAC,eAAe,WAAW,eAAe,QAAQ,mBACjD;;sCACE,8OAAC;4BAAI,WAAU;4BAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;8CAC1F,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;wCAAC;qCAAU;oCAClB,eAAe,CAAC,QAAU,aAAa,KAAK,CAAC,EAAE;oCAC/C,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;wBAG9C,eAAe,yBACd,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAW;;;;;;;;;;;;;;;;;;;;;sDAK7C,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;gBASvB,CAAC,eAAe,eAAe,eAAe,QAAQ,mBACrD,8OAAC;oBAAI,WAAU;oBAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCAC1F,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,2HAAA,CAAA,SAAM;4BACL,OAAO;gCAAC;6BAAY;4BACpB,eAAe,CAAC,QAAU,eAAe,KAAK,CAAC,EAAE;4BACjD,KAAK;4BACL,KAAK;4BACL,MAAM;4BACN,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAA6B;;;;;;;;;;;;8BAIjD,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAAoC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCACtF,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;sCAGZ,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,OAAO,EAAE;wCACrC,WAAU;kDAGV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGjB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGpB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGpB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGhB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 2117, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal>\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg\",\n          \"glass-card border-0 shadow-2xl\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none\">\n          <XIcon className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\n        \"flex flex-col space-y-1.5 text-center sm:text-left\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\n        \"text-lg font-semibold leading-none tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2KACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA,kCACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON>con, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger>) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus:ring-ring flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background focus:outline-none focus:ring-1 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n        \"glass-card border-0\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"h-4 w-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"h-4 w-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"h-4 w-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n          \"glass-card border-0 shadow-2xl\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          data-slot=\"select-viewport\"\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"h-4 w-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2TACA,uBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,kCACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,aAAU;oBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/BoardSizeModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { useImageEditor, BoardSize, Unit } from './context';\n\n// Unit conversion to pixels (approximate values for display purposes)\nconst UNIT_TO_PX: Record<Unit, number> = {\n  px: 1,\n  cm: 37.8, // 1 cm ≈ 37.8 pixels at 96 DPI\n  in: 96,   // 1 inch = 96 pixels at 96 DPI\n  ft: 1152  // 1 foot = 12 inches = 1152 pixels at 96 DPI\n};\n\n// Common preset sizes for different units\nconst PRESETS: Record<Unit, Array<{ name: string; width: number; height: number }>> = {\n  px: [\n    { name: 'Small Canvas', width: 800, height: 600 },\n    { name: 'Medium Canvas', width: 1200, height: 900 },\n    { name: 'Large Canvas', width: 1920, height: 1080 },\n    { name: 'Square Small', width: 800, height: 800 },\n    { name: 'Square Large', width: 1200, height: 1200 },\n  ],\n  cm: [\n    { name: 'A4 Portrait', width: 21, height: 29.7 },\n    { name: 'A4 Landscape', width: 29.7, height: 21 },\n    { name: 'A3 Portrait', width: 29.7, height: 42 },\n    { name: 'Letter Portrait', width: 21.6, height: 27.9 },\n    { name: 'Square 20x20', width: 20, height: 20 },\n  ],\n  in: [\n    { name: 'Letter Portrait', width: 8.5, height: 11 },\n    { name: 'Letter Landscape', width: 11, height: 8.5 },\n    { name: 'Legal Portrait', width: 8.5, height: 14 },\n    { name: 'Tabloid', width: 11, height: 17 },\n    { name: 'Square 8x8', width: 8, height: 8 },\n  ],\n  ft: [\n    { name: 'Small Banner', width: 3, height: 2 },\n    { name: 'Medium Banner', width: 6, height: 4 },\n    { name: 'Large Banner', width: 10, height: 6 },\n    { name: 'Billboard', width: 14, height: 8 },\n    { name: 'Square 4x4', width: 4, height: 4 },\n  ],\n};\n\nexport default function BoardSizeModal() {\n  const { showBoardSizeModal, setShowBoardSizeModal, setBoardSize } = useImageEditor();\n  const [width, setWidth] = useState<string>('800');\n  const [height, setHeight] = useState<string>('600');\n  const [unit, setUnit] = useState<Unit>('px');\n  const [selectedPreset, setSelectedPreset] = useState<string>('');\n\n  const handlePresetSelect = (presetName: string) => {\n    const preset = PRESETS[unit].find(p => p.name === presetName);\n    if (preset) {\n      setWidth(preset.width.toString());\n      setHeight(preset.height.toString());\n      setSelectedPreset(presetName);\n    }\n  };\n\n  const handleUnitChange = (newUnit: Unit) => {\n    setUnit(newUnit);\n    setSelectedPreset(''); // Clear preset selection when unit changes\n    // Reset to default values for the new unit\n    const defaultPreset = PRESETS[newUnit][0];\n    setWidth(defaultPreset.width.toString());\n    setHeight(defaultPreset.height.toString());\n  };\n\n  const handleCreate = () => {\n    const widthNum = parseFloat(width);\n    const heightNum = parseFloat(height);\n    \n    if (isNaN(widthNum) || isNaN(heightNum) || widthNum <= 0 || heightNum <= 0) {\n      alert('Please enter valid positive numbers for width and height.');\n      return;\n    }\n\n    const boardSize: BoardSize = {\n      width: widthNum,\n      height: heightNum,\n      unit\n    };\n\n    setBoardSize(boardSize);\n    setShowBoardSizeModal(false);\n  };\n\n  const convertedWidth = Math.round(parseFloat(width || '0') * UNIT_TO_PX[unit]);\n  const convertedHeight = Math.round(parseFloat(height || '0') * UNIT_TO_PX[unit]);\n\n  return (\n    <Dialog open={showBoardSizeModal} onOpenChange={setShowBoardSizeModal}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"text-white\">Create New Board</DialogTitle>\n          <DialogDescription className=\"text-white/70\">\n            Set the size for your new design board. Choose from presets or enter custom dimensions.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Unit Selection */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"unit\" className=\"text-white\">Unit</Label>\n            <Select value={unit} onValueChange={handleUnitChange}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"px\">Pixels (px)</SelectItem>\n                <SelectItem value=\"cm\">Centimeters (cm)</SelectItem>\n                <SelectItem value=\"in\">Inches (in)</SelectItem>\n                <SelectItem value=\"ft\">Feet (ft)</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Preset Selection */}\n          <div className=\"space-y-2\">\n            <Label className=\"text-white\">Presets</Label>\n            <Select value={selectedPreset} onValueChange={handlePresetSelect}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"Choose a preset (optional)\" />\n              </SelectTrigger>\n              <SelectContent>\n                {PRESETS[unit].map((preset) => (\n                  <SelectItem key={preset.name} value={preset.name}>\n                    {preset.name} ({preset.width} × {preset.height} {unit})\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Custom Dimensions */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"width\" className=\"text-white\">Width ({unit})</Label>\n              <Input\n                id=\"width\"\n                type=\"number\"\n                value={width}\n                onChange={(e) => {\n                  setWidth(e.target.value);\n                  setSelectedPreset(''); // Clear preset when manually editing\n                }}\n                placeholder=\"Enter width\"\n                min=\"1\"\n                step=\"0.1\"\n                className=\"glass-card border-0 text-white placeholder:text-white/50\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"height\" className=\"text-white\">Height ({unit})</Label>\n              <Input\n                id=\"height\"\n                type=\"number\"\n                value={height}\n                onChange={(e) => {\n                  setHeight(e.target.value);\n                  setSelectedPreset(''); // Clear preset when manually editing\n                }}\n                placeholder=\"Enter height\"\n                min=\"1\"\n                step=\"0.1\"\n                className=\"glass-card border-0 text-white placeholder:text-white/50\"\n              />\n            </div>\n          </div>\n\n          {/* Preview Info */}\n          {width && height && !isNaN(parseFloat(width)) && !isNaN(parseFloat(height)) && (\n            <div className=\"p-3 rounded-lg bg-white/5 border border-white/10\">\n              <p className=\"text-sm text-white/70\">\n                Canvas size: {width} × {height} {unit}\n                {unit !== 'px' && (\n                  <span className=\"block\">\n                    ≈ {convertedWidth} × {convertedHeight} pixels\n                  </span>\n                )}\n              </p>\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex justify-end space-x-2\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowBoardSizeModal(false)}\n              className=\"glass-card border-white/20 text-white hover:bg-white/10\"\n            >\n              Cancel\n            </Button>\n            <Button\n              onClick={handleCreate}\n              className=\"bg-primary hover:bg-primary/90 text-white\"\n            >\n              Create Board\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,sEAAsE;AACtE,MAAM,aAAmC;IACvC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAM,6CAA6C;AACzD;AAEA,0CAA0C;AAC1C,MAAM,UAAgF;IACpF,IAAI;QACF;YAAE,MAAM;YAAgB,OAAO;YAAK,QAAQ;QAAI;QAChD;YAAE,MAAM;YAAiB,OAAO;YAAM,QAAQ;QAAI;QAClD;YAAE,MAAM;YAAgB,OAAO;YAAM,QAAQ;QAAK;QAClD;YAAE,MAAM;YAAgB,OAAO;YAAK,QAAQ;QAAI;QAChD;YAAE,MAAM;YAAgB,OAAO;YAAM,QAAQ;QAAK;KACnD;IACD,IAAI;QACF;YAAE,MAAM;YAAe,OAAO;YAAI,QAAQ;QAAK;QAC/C;YAAE,MAAM;YAAgB,OAAO;YAAM,QAAQ;QAAG;QAChD;YAAE,MAAM;YAAe,OAAO;YAAM,QAAQ;QAAG;QAC/C;YAAE,MAAM;YAAmB,OAAO;YAAM,QAAQ;QAAK;QACrD;YAAE,MAAM;YAAgB,OAAO;YAAI,QAAQ;QAAG;KAC/C;IACD,IAAI;QACF;YAAE,MAAM;YAAmB,OAAO;YAAK,QAAQ;QAAG;QAClD;YAAE,MAAM;YAAoB,OAAO;YAAI,QAAQ;QAAI;QACnD;YAAE,MAAM;YAAkB,OAAO;YAAK,QAAQ;QAAG;QACjD;YAAE,MAAM;YAAW,OAAO;YAAI,QAAQ;QAAG;QACzC;YAAE,MAAM;YAAc,OAAO;YAAG,QAAQ;QAAE;KAC3C;IACD,IAAI;QACF;YAAE,MAAM;YAAgB,OAAO;YAAG,QAAQ;QAAE;QAC5C;YAAE,MAAM;YAAiB,OAAO;YAAG,QAAQ;QAAE;QAC7C;YAAE,MAAM;YAAgB,OAAO;YAAI,QAAQ;QAAE;QAC7C;YAAE,MAAM;YAAa,OAAO;YAAI,QAAQ;QAAE;QAC1C;YAAE,MAAM;YAAc,OAAO;YAAG,QAAQ;QAAE;KAC3C;AACH;AAEe,SAAS;IACtB,MAAM,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAClD,IAAI,QAAQ;YACV,SAAS,OAAO,KAAK,CAAC,QAAQ;YAC9B,UAAU,OAAO,MAAM,CAAC,QAAQ;YAChC,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;QACR,kBAAkB,KAAK,2CAA2C;QAClE,2CAA2C;QAC3C,MAAM,gBAAgB,OAAO,CAAC,QAAQ,CAAC,EAAE;QACzC,SAAS,cAAc,KAAK,CAAC,QAAQ;QACrC,UAAU,cAAc,MAAM,CAAC,QAAQ;IACzC;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,WAAW;QAC5B,MAAM,YAAY,WAAW;QAE7B,IAAI,MAAM,aAAa,MAAM,cAAc,YAAY,KAAK,aAAa,GAAG;YAC1E,MAAM;YACN;QACF;QAEA,MAAM,YAAuB;YAC3B,OAAO;YACP,QAAQ;YACR;QACF;QAEA,aAAa;QACb,sBAAsB;IACxB;IAEA,MAAM,iBAAiB,KAAK,KAAK,CAAC,WAAW,SAAS,OAAO,UAAU,CAAC,KAAK;IAC7E,MAAM,kBAAkB,KAAK,KAAK,CAAC,WAAW,UAAU,OAAO,UAAU,CAAC,KAAK;IAE/E,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAoB,cAAc;kBAC9C,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;sCAAa;;;;;;sCACpC,8OAAC,2HAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAgB;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,WAAU;8CAAa;;;;;;8CAC7C,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAM,eAAe;;sDAClC,8OAAC,2HAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,2HAAA,CAAA,gBAAa;;8DACZ,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAM7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAa;;;;;;8CAC9B,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,8OAAC,2HAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,2HAAA,CAAA,gBAAa;sDACX,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,uBAClB,8OAAC,2HAAA,CAAA,aAAU;oDAAmB,OAAO,OAAO,IAAI;;wDAC7C,OAAO,IAAI;wDAAC;wDAAG,OAAO,KAAK;wDAAC;wDAAI,OAAO,MAAM;wDAAC;wDAAE;wDAAK;;mDADvC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;sCASpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAQ,WAAU;;gDAAa;gDAAQ;gDAAK;;;;;;;sDAC3D,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC;gDACT,SAAS,EAAE,MAAM,CAAC,KAAK;gDACvB,kBAAkB,KAAK,qCAAqC;4CAC9D;4CACA,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAS,WAAU;;gDAAa;gDAAS;gDAAK;;;;;;;sDAC7D,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC;gDACT,UAAU,EAAE,MAAM,CAAC,KAAK;gDACxB,kBAAkB,KAAK,qCAAqC;4CAC9D;4CACA,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,WAAU;;;;;;;;;;;;;;;;;;wBAMf,SAAS,UAAU,CAAC,MAAM,WAAW,WAAW,CAAC,MAAM,WAAW,0BACjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACrB;oCAAM;oCAAI;oCAAO;oCAAE;oCAChC,SAAS,sBACR,8OAAC;wCAAK,WAAU;;4CAAQ;4CACnB;4CAAe;4CAAI;4CAAgB;;;;;;;;;;;;;;;;;;sCAQhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CACX;;;;;;8CAGD,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3063, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ImageEditorProvider } from './context';\r\nimport Canvas from './Canvas';\r\nimport Toolbar from './Toolbar';\r\nimport BoardSizeModal from './BoardSizeModal';\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ImageEditorProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ImageEditor = ({ children }: ImageEditorProps) => {\r\n  return (\r\n    <ImageEditorProvider>\r\n      {children}\r\n      <BoardSizeModal />\r\n    </ImageEditorProvider>\r\n  );\r\n};\r\n\r\nImageEditor.Canvas = Canvas;\r\nImageEditor.Toolbar = Toolbar;\r\n\r\nexport default ImageEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAoB;IACjD,qBACE,8OAAC,+IAAA,CAAA,sBAAmB;;YACjB;0BACD,8OAAC,sJAAA,CAAA,UAAc;;;;;;;;;;;AAGrB;AAEA,YAAY,MAAM,GAAG,8IAAA,CAAA,UAAM;AAC3B,YAAY,OAAO,GAAG,+IAAA,CAAA,UAAO;uCAEd", "debugId": null}}, {"offset": {"line": 3100, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/design/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ImageEditor from '../../../../components/design/ImageEditor';\r\n\r\nexport default function DesignPage() {\r\n  return (\r\n    <ImageEditor>\r\n      <ImageEditor.Canvas />\r\n      <ImageEditor.Toolbar />\r\n    </ImageEditor>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC,6IAAA,CAAA,UAAW;;0BACV,8OAAC,6IAAA,CAAA,UAAW,CAAC,MAAM;;;;;0BACnB,8OAAC,6IAAA,CAAA,UAAW,CAAC,OAAO;;;;;;;;;;;AAG1B", "debugId": null}}]}