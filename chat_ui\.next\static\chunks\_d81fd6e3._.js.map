{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\nimport * as fabric from 'fabric';\r\n\r\nexport type Tool = 'select' | 'rectangle' | 'circle' | 'text' | 'brush' | 'eraser' | 'image' | 'hand' | 'selection';\r\n\r\n// Define the shape of the context state\r\ninterface ImageEditorContextType {\r\n  canvas: fabric.Canvas | null;\r\n  setCanvas: (canvas: fabric.Canvas | null) => void;\r\n  activeTool: Tool;\r\n  setActiveTool: (tool: Tool) => void;\r\n  brushSize: number;\r\n  setBrushSize: (size: number) => void;\r\n  brushColor: string;\r\n  setBrushColor: (color: string) => void;\r\n  fillColor: string;\r\n  setFillColor: (color: string) => void;\r\n  strokeColor: string;\r\n  setStrokeColor: (color: string) => void;\r\n  strokeWidth: number;\r\n  setStrokeWidth: (width: number) => void;\r\n  isDrawing: boolean;\r\n  setIsDrawing: (drawing: boolean) => void;\r\n  // Canvas viewport state\r\n  zoom: number;\r\n  setZoom: (zoom: number) => void;\r\n  panX: number;\r\n  setPanX: (x: number) => void;\r\n  panY: number;\r\n  setPanY: (y: number) => void;\r\n  // Toolbar position\r\n  toolbarPosition: { x: number; y: number };\r\n  setToolbarPosition: (position: { x: number; y: number }) => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ImageEditorContext = createContext<ImageEditorContextType | null>(null);\r\n\r\n// Create a provider component\r\ninterface ImageEditorProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {\r\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\r\n  const [activeTool, setActiveTool] = useState<Tool>('select');\r\n  const [brushSize, setBrushSize] = useState(5);\r\n  const [brushColor, setBrushColor] = useState('#000000');\r\n  const [fillColor, setFillColor] = useState('#ff0000');\r\n  const [strokeColor, setStrokeColor] = useState('#000000');\r\n  const [strokeWidth, setStrokeWidth] = useState(2);\r\n  const [isDrawing, setIsDrawing] = useState(false);\r\n\r\n  // Canvas viewport state\r\n  const [zoom, setZoom] = useState(1);\r\n  const [panX, setPanX] = useState(0);\r\n  const [panY, setPanY] = useState(0);\r\n\r\n  // Toolbar position\r\n  const [toolbarPosition, setToolbarPosition] = useState({ x: 500, y: 60 });\r\n\r\n  return (\r\n    <ImageEditorContext.Provider value={{\r\n      canvas,\r\n      setCanvas,\r\n      activeTool,\r\n      setActiveTool,\r\n      brushSize,\r\n      setBrushSize,\r\n      brushColor,\r\n      setBrushColor,\r\n      fillColor,\r\n      setFillColor,\r\n      strokeColor,\r\n      setStrokeColor,\r\n      strokeWidth,\r\n      setStrokeWidth,\r\n      isDrawing,\r\n      setIsDrawing,\r\n      zoom,\r\n      setZoom,\r\n      panX,\r\n      setPanX,\r\n      panY,\r\n      setPanY,\r\n      toolbarPosition,\r\n      setToolbarPosition\r\n    }}>\r\n      {children}\r\n    </ImageEditorContext.Provider>\r\n  );\r\n};\r\n\r\n// Create a custom hook to use the context\r\nexport const useImageEditor = () => {\r\n  const context = useContext(ImageEditorContext);\r\n  if (!context) {\r\n    throw new Error('useImageEditor must be used within an ImageEditorProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAqCA,0CAA0C;AAC1C,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAOjE,MAAM,sBAAsB;QAAC,EAAE,QAAQ,EAA4B;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAG;IAEvE,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAhDa;KAAA;AAmDN,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,SAAS,OAAO,KAOoC;QAPpC,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C,GAPpC;;IAQd,MAAM,UAAU,6JAAA,CAAA,UAAa;mCAC3B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;gBAAC;gBAAK;aAAI;kCAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf;GArDS;KAAA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Canvas.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useCallback, useState } from 'react';\r\nimport * as fabric from 'fabric';\r\nimport { useImageEditor } from './context';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\n\r\nconst Canvas = () => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\r\n  const [isPanning, setIsPanning] = useState(false);\r\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\r\n  const [selectionRect, setSelectionRect] = useState<fabric.Rect | null>(null);\r\n\r\n  const {\r\n    canvas,\r\n    setCanvas,\r\n    activeTool,\r\n    brushSize,\r\n    brushColor,\r\n    fillColor,\r\n    strokeColor,\r\n    strokeWidth,\r\n    zoom,\r\n    setZoom,\r\n    panX,\r\n    setPanX,\r\n    panY,\r\n    setPanY\r\n  } = useImageEditor();\r\n\r\n  // Handle window resize to make canvas fullscreen\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setDimensions({\r\n        width: window.innerWidth,\r\n        height: window.innerHeight\r\n      });\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Handle wheel events for zooming and panning\r\n  const handleWheel = useCallback((e: WheelEvent) => {\r\n    e.preventDefault();\r\n    if (!canvas) return;\r\n\r\n    if (e.ctrlKey || e.metaKey) {\r\n      // Zoom towards the mouse pointer\r\n      const delta = e.deltaY > 0 ? 0.9 : 1.1;\r\n      const newZoom = Math.max(0.01, Math.min(100, canvas.getZoom() * delta));\r\n      const point = new fabric.Point(e.offsetX, e.offsetY);\r\n      canvas.zoomToPoint(point, newZoom);\r\n    } else {\r\n      // Pan with the wheel\r\n      const delta = new fabric.Point(-e.deltaX, -e.deltaY);\r\n      canvas.relativePan(delta);\r\n    }\r\n\r\n    // Sync state with canvas viewport\r\n    const vpt = canvas.viewportTransform;\r\n    if (vpt) {\r\n      setZoom(vpt[0]);\r\n      setPanX(vpt[4]);\r\n      setPanY(vpt[5]);\r\n    }\r\n    canvas.renderAll();\r\n  }, [canvas, setZoom, setPanX, setPanY]);\r\n\r\n  // Handle panning with space + drag or hand tool\r\n  const handleMouseDown = useCallback((e: MouseEvent) => {\r\n    if (activeTool === 'hand' || e.button === 1) {\r\n      setIsPanning(true);\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n      e.preventDefault();\r\n    }\r\n  }, [activeTool]);\r\n\r\n  const handleMouseMove = useCallback((e: MouseEvent) => {\r\n    if (isPanning && canvas) {\r\n      const deltaX = e.clientX - lastPanPoint.x;\r\n      const deltaY = e.clientY - lastPanPoint.y;\r\n\r\n      canvas.relativePan(new fabric.Point(deltaX, deltaY));\r\n      \r\n      // Sync state with canvas viewport\r\n      const vpt = canvas.viewportTransform;\r\n      if (vpt) {\r\n        setPanX(vpt[4]);\r\n        setPanY(vpt[5]);\r\n      }\r\n\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n    }\r\n  }, [isPanning, lastPanPoint, canvas, setPanX, setPanY]);\r\n\r\n  const handleMouseUp = useCallback(() => {\r\n    setIsPanning(false);\r\n  }, []);\r\n\r\n  // Handle keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      // Space key for temporary hand tool\r\n      if (e.code === 'Space' && !e.repeat) {\r\n        e.preventDefault();\r\n        document.body.style.cursor = 'grab';\r\n      }\r\n\r\n      // Zoom shortcuts\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '0') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          // Reset zoom to 100% and center the view\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, 1);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom In (Ctrl/Cmd + Plus/Equal)\r\n      if ((e.ctrlKey || e.metaKey) && (e.key === '=' || e.key === '+')) {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          const newZoom = Math.min(100, canvas.getZoom() * 1.25);\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, newZoom);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom Out (Ctrl/Cmd + Minus)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '-') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          const newZoom = Math.max(0.01, canvas.getZoom() * 0.8);\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, newZoom);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Fit to Screen (Ctrl/Cmd + 1)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '1') {\r\n        e.preventDefault();\r\n        if (canvas) {\r\n          // This can be customized to fit objects, for now, it resets to 100%\r\n          const center = new fabric.Point(canvas.getWidth() / 2, canvas.getHeight() / 2);\r\n          canvas.zoomToPoint(center, 1);\r\n          const vpt = canvas.viewportTransform;\r\n          if (vpt) {\r\n            setZoom(vpt[0]);\r\n            setPanX(vpt[4]);\r\n            setPanY(vpt[5]);\r\n          }\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleKeyUp = (e: KeyboardEvent) => {\r\n      if (e.code === 'Space') {\r\n        document.body.style.cursor = 'default';\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    window.addEventListener('keyup', handleKeyUp);\r\n\r\n    return () => {\r\n      window.removeEventListener('keydown', handleKeyDown);\r\n      window.removeEventListener('keyup', handleKeyUp);\r\n    };\r\n  }, [canvas, setZoom]);\r\n\r\n  // Update drawing mode when tool changes\r\n  useEffect(() => {\r\n    if (!canvas) return;\r\n\r\n    // Store the active tool and properties on the canvas for event handlers\r\n    (canvas as any).activeTool = activeTool;\r\n    (canvas as any).fillColor = fillColor;\r\n    (canvas as any).strokeColor = strokeColor;\r\n    (canvas as any).strokeWidth = strokeWidth;\r\n\r\n    if (activeTool === 'brush') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        canvas.freeDrawingBrush.color = brushColor;\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    } else if (activeTool === 'selection') {\r\n      canvas.isDrawingMode = false;\r\n      canvas.selection = true;\r\n      canvas.defaultCursor = 'crosshair';\r\n    }\r\n    else if (activeTool === 'eraser') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        // Set eraser mode by using destination-out composite operation\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'destination-out';\r\n      }\r\n    } else {\r\n      canvas.isDrawingMode = false;\r\n      // Reset composite operation\r\n      if (canvas.freeDrawingBrush) {\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    }\r\n  }, [canvas, activeTool, brushSize, brushColor, fillColor, strokeColor, strokeWidth]);\r\n\r\n  const setupCanvasEvents = useCallback((canvas: fabric.Canvas) => {\r\n    // Handle mouse events for shape creation\r\n    let isDown = false;\r\n    let origX = 0;\r\n    let origY = 0;\r\n    let shape: fabric.Object | null = null;\r\n\r\n    const onMouseDown = (o: any) => {\r\n      // Get current tool from the canvas data or use a closure\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n      if (currentTool === 'select' || currentTool === 'brush' || currentTool === 'eraser' || currentTool === 'hand') return;\r\n\r\n      isDown = true;\r\n      const pointer = canvas.getPointer(o.e);\r\n      origX = pointer.x;\r\n      origY = pointer.y;\r\n\r\n      if (currentTool === 'selection') {\r\n        canvas.selection = false;\r\n        const rect = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: 'rgba(0,0,0,0.3)',\r\n          stroke: 'black',\r\n          strokeDashArray: [5, 5],\r\n          selectable: true,\r\n        });\r\n        setSelectionRect(rect);\r\n        canvas.add(rect);\r\n        return; // Prevent creating other shapes\r\n      }\r\n\r\n      if (currentTool === 'rectangle') {\r\n        shape = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      } else if (currentTool === 'circle') {\r\n        shape = new fabric.Circle({\r\n          left: origX,\r\n          top: origY,\r\n          radius: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      }\r\n\r\n      if (shape) {\r\n        canvas.add(shape);\r\n      }\r\n    };\r\n\r\n    const onMouseMove = (o: any) => {\r\n      if (!isDown) return;\r\n\r\n      const pointer = canvas.getPointer(o.e);\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n\r\n      if (currentTool === 'selection' && selectionRect) {\r\n        const rect = selectionRect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (currentTool === 'rectangle' && shape) {\r\n        const rect = shape as fabric.Rect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (currentTool === 'circle') {\r\n        const circle = shape as fabric.Circle;\r\n        const radius = Math.sqrt(Math.pow(pointer.x - origX, 2) + Math.pow(pointer.y - origY, 2)) / 2;\r\n        circle.set({ radius });\r\n      }\r\n\r\n      canvas.renderAll();\r\n    };\r\n\r\n    const onMouseUp = () => {\r\n      isDown = false;\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n\r\n      if (currentTool === 'selection' && selectionRect) {\r\n        cropToSelection(selectionRect);\r\n        canvas.remove(selectionRect);\r\n        setSelectionRect(null);\r\n        canvas.selection = true; // Re-enable selection\r\n      }\r\n\r\n      if (shape) {\r\n        shape.set({ selectable: true });\r\n        shape = null;\r\n      }\r\n      isDown = false;\r\n    };\r\n\r\n    canvas.on('mouse:down', onMouseDown);\r\n    canvas.on('mouse:move', onMouseMove);\r\n    canvas.on('mouse:up', onMouseUp);\r\n\r\n    return () => {\r\n      canvas.off('mouse:down', onMouseDown);\r\n      canvas.off('mouse:move', onMouseMove);\r\n      canvas.off('mouse:up', onMouseUp);\r\n    };\r\n  }, [activeTool, fillColor, strokeColor, strokeWidth, selectionRect]);\r\n\r\n  const cropToSelection = (rect: fabric.Rect) => {\r\n    if (!canvas || !rect.width || !rect.height) return;\r\n\r\n    const cropped = new Image();\r\n    cropped.src = canvas.toDataURL({\r\n      left: rect.left,\r\n      top: rect.top,\r\n      width: rect.width,\r\n      height: rect.height,\r\n      multiplier: 1,\r\n    });\r\n\r\n    cropped.onload = () => {\r\n      canvas.clear();\r\n      const image = new fabric.FabricImage(cropped);\r\n      canvas.setWidth(rect.width || 0);\r\n      canvas.setHeight(rect.height || 0);\r\n      canvas.add(image);\r\n      canvas.renderAll();\r\n    };\r\n  };\r\n\r\n  // Initialize canvas when dimensions are available\r\n  useEffect(() => {\r\n    if (canvasRef.current && dimensions.width > 0 && dimensions.height > 0) {\r\n      const fabricCanvas = new fabric.Canvas(canvasRef.current, {\r\n        width: dimensions.width,\r\n        height: dimensions.height,\r\n        backgroundColor: 'transparent',\r\n      });\r\n\r\n      // Enable viewport transform for panning and zooming\r\n      fabricCanvas.selection = true;\r\n\r\n      setCanvas(fabricCanvas);\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        fabricCanvas.dispose();\r\n      };\r\n    }\r\n  }, [dimensions, setCanvas]);\r\n\r\n  // Setup events once when canvas is created\r\n  useEffect(() => {\r\n    if (canvas) {\r\n      const cleanup = setupCanvasEvents(canvas);\r\n      return cleanup;\r\n    }\r\n  }, [canvas, setupCanvasEvents]);\r\n\r\n  // Add event listeners for mouse and wheel events\r\n  useEffect(() => {\r\n    const container = containerRef.current;\r\n    if (!container) return;\r\n\r\n    container.addEventListener('wheel', handleWheel, { passive: false });\r\n    container.addEventListener('mousedown', handleMouseDown);\r\n    container.addEventListener('mousemove', handleMouseMove);\r\n    container.addEventListener('mouseup', handleMouseUp);\r\n\r\n    return () => {\r\n      container.removeEventListener('wheel', handleWheel);\r\n      container.removeEventListener('mousedown', handleMouseDown);\r\n      container.removeEventListener('mousemove', handleMouseMove);\r\n      container.removeEventListener('mouseup', handleMouseUp);\r\n    };\r\n  }, [handleWheel, handleMouseDown, handleMouseMove, handleMouseUp]);\r\n\r\n  const getCursor = () => {\r\n    if (isPanning) return 'grabbing';\r\n    switch (activeTool) {\r\n      case 'hand':\r\n        return 'grab';\r\n      case 'rectangle':\r\n      case 'circle':\r\n      case 'selection':\r\n      case 'brush':\r\n      case 'eraser':\r\n        return 'crosshair';\r\n      case 'text':\r\n        return 'text';\r\n      case 'select':\r\n      default:\r\n        return 'default';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"fixed inset-0 overflow-hidden\"\r\n      style={{\r\n        background: `\r\n          radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n          radial-gradient(circle at 75% 75%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n          linear-gradient(135deg, #667eea 0%, #764ba2 100%)\r\n        `,\r\n        cursor: getCursor()\r\n      }}\r\n    >\r\n      {/* Infinite grid background */}\r\n      <div\r\n        className=\"absolute inset-0\"\r\n        style={{\r\n          backgroundImage: `\r\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\r\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\r\n          `,\r\n          backgroundSize: `20px 20px`,\r\n          backgroundPosition: `${panX}px ${panY}px`,\r\n        }}\r\n      />\r\n\r\n      {/* Canvas */}\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"absolute top-0 left-0 z-10\"\r\n        style={{\r\n          display: 'block',\r\n          width: dimensions.width,\r\n          height: dimensions.height\r\n        }}\r\n      />\r\n\r\n      {/* Zoom indicator with interactive slider */}\r\n      <div className=\"fixed bottom-4 right-4 z-50\">\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <button className=\"bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm hover:bg-black/60 transition-colors cursor-pointer\">\r\n              {Math.round(zoom * 100)}%\r\n            </button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-80 bg-black/80 backdrop-blur-md border-white/20\" side=\"top\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-white\">Zoom Level</label>\r\n                <span className=\"text-sm text-white/70\">{Math.round(zoom * 100)}%</span>\r\n              </div>\r\n              <Slider\r\n                value={[zoom * 100]}\r\n                onValueChange={(value) => {\r\n                  const newZoom = value[0] / 100;\r\n                  setZoom(newZoom);\r\n                  if (canvas) {\r\n                    canvas.setZoom(newZoom);\r\n                    canvas.renderAll();\r\n                  }\r\n                }}\r\n                max={10000}\r\n                min={1}\r\n                step={1}\r\n                className=\"flex-1\"\r\n              />\r\n              {/* Zoom Presets */}\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex gap-1 flex-wrap justify-center\">\r\n                  {[25, 50, 75, 100, 125, 150, 200, 400, 800, 1600].map((percent) => (\r\n                    <button\r\n                      key={percent}\r\n                      onClick={() => {\r\n                        const newZoom = percent / 100;\r\n                        setZoom(newZoom);\r\n                        if (canvas) {\r\n                          canvas.setZoom(newZoom);\r\n                          canvas.renderAll();\r\n                        }\r\n                      }}\r\n                      className={`px-2 py-1 text-xs rounded transition-colors ${\r\n                        Math.round(zoom * 100) === percent\r\n                          ? 'bg-white/30 text-white font-medium'\r\n                          : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-white'\r\n                      }`}\r\n                    >\r\n                      {percent}%\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Range Labels */}\r\n                <div className=\"flex items-center justify-between text-xs text-white/50\">\r\n                  <span>1%</span>\r\n                  <span className=\"text-white/70\">Professional Zoom Range</span>\r\n                  <span>10000%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Canvas;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEvE,MAAM,EACJ,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACR,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEjB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc;wBACZ,OAAO,OAAO,UAAU;wBACxB,QAAQ,OAAO,WAAW;oBAC5B;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC;YAC/B,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;YAEb,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;gBAC1B,iCAAiC;gBACjC,MAAM,QAAQ,EAAE,MAAM,GAAG,IAAI,MAAM;gBACnC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO,OAAO,KAAK;gBAChE,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAY,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;gBACnD,OAAO,WAAW,CAAC,OAAO;YAC5B,OAAO;gBACL,qBAAqB;gBACrB,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAY,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;gBACnD,OAAO,WAAW,CAAC;YACrB;YAEA,kCAAkC;YAClC,MAAM,MAAM,OAAO,iBAAiB;YACpC,IAAI,KAAK;gBACP,QAAQ,GAAG,CAAC,EAAE;gBACd,QAAQ,GAAG,CAAC,EAAE;gBACd,QAAQ,GAAG,CAAC,EAAE;YAChB;YACA,OAAO,SAAS;QAClB;0CAAG;QAAC;QAAQ;QAAS;QAAS;KAAQ;IAEtC,gDAAgD;IAChD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YACnC,IAAI,eAAe,UAAU,EAAE,MAAM,KAAK,GAAG;gBAC3C,aAAa;gBACb,gBAAgB;oBAAE,GAAG,EAAE,OAAO;oBAAE,GAAG,EAAE,OAAO;gBAAC;gBAC7C,EAAE,cAAc;YAClB;QACF;8CAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YACnC,IAAI,aAAa,QAAQ;gBACvB,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBAEzC,OAAO,WAAW,CAAC,IAAI,kJAAA,CAAA,QAAY,CAAC,QAAQ;gBAE5C,kCAAkC;gBAClC,MAAM,MAAM,OAAO,iBAAiB;gBACpC,IAAI,KAAK;oBACP,QAAQ,GAAG,CAAC,EAAE;oBACd,QAAQ,GAAG,CAAC,EAAE;gBAChB;gBAEA,gBAAgB;oBAAE,GAAG,EAAE,OAAO;oBAAE,GAAG,EAAE,OAAO;gBAAC;YAC/C;QACF;8CAAG;QAAC;QAAW;QAAc;QAAQ;QAAS;KAAQ;IAEtD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAChC,aAAa;QACf;4CAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;kDAAgB,CAAC;oBACrB,oCAAoC;oBACpC,IAAI,EAAE,IAAI,KAAK,WAAW,CAAC,EAAE,MAAM,EAAE;wBACnC,EAAE,cAAc;wBAChB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;oBAC/B;oBAEA,iBAAiB;oBACjB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,IAAI,QAAQ;4BACV,yCAAyC;4BACzC,MAAM,SAAS,IAAI,kJAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;4BAC5E,OAAO,WAAW,CAAC,QAAQ;4BAC3B,MAAM,MAAM,OAAO,iBAAiB;4BACpC,IAAI,KAAK;gCACP,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;4BAChB;4BACA,OAAO,SAAS;wBAClB;oBACF;oBAEA,kCAAkC;oBAClC,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,GAAG,GAAG;wBAChE,EAAE,cAAc;wBAChB,IAAI,QAAQ;4BACV,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,OAAO,OAAO,KAAK;4BACjD,MAAM,SAAS,IAAI,kJAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;4BAC5E,OAAO,WAAW,CAAC,QAAQ;4BAC3B,MAAM,MAAM,OAAO,iBAAiB;4BACpC,IAAI,KAAK;gCACP,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;4BAChB;4BACA,OAAO,SAAS;wBAClB;oBACF;oBAEA,8BAA8B;oBAC9B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,IAAI,QAAQ;4BACV,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,OAAO,OAAO,KAAK;4BAClD,MAAM,SAAS,IAAI,kJAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;4BAC5E,OAAO,WAAW,CAAC,QAAQ;4BAC3B,MAAM,MAAM,OAAO,iBAAiB;4BACpC,IAAI,KAAK;gCACP,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;4BAChB;4BACA,OAAO,SAAS;wBAClB;oBACF;oBAEA,+BAA+B;oBAC/B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,IAAI,QAAQ;4BACV,oEAAoE;4BACpE,MAAM,SAAS,IAAI,kJAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,KAAK,GAAG,OAAO,SAAS,KAAK;4BAC5E,OAAO,WAAW,CAAC,QAAQ;4BAC3B,MAAM,MAAM,OAAO,iBAAiB;4BACpC,IAAI,KAAK;gCACP,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;gCACd,QAAQ,GAAG,CAAC,EAAE;4BAChB;4BACA,OAAO,SAAS;wBAClB;oBACF;gBACF;;YAEA,MAAM;gDAAc,CAAC;oBACnB,IAAI,EAAE,IAAI,KAAK,SAAS;wBACtB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;oBAC/B;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC,OAAO,gBAAgB,CAAC,SAAS;YAEjC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,SAAS;gBACtC;;QACF;2BAAG;QAAC;QAAQ;KAAQ;IAEpB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,QAAQ;YAEb,wEAAwE;YACvE,OAAe,UAAU,GAAG;YAC5B,OAAe,SAAS,GAAG;YAC3B,OAAe,WAAW,GAAG;YAC7B,OAAe,WAAW,GAAG;YAE9B,IAAI,eAAe,SAAS;gBAC1B,OAAO,aAAa,GAAG;gBACvB,IAAI,OAAO,gBAAgB,EAAE;oBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;oBAChC,OAAO,gBAAgB,CAAC,KAAK,GAAG;oBAC/B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;gBAC9D;YACF,OAAO,IAAI,eAAe,aAAa;gBACrC,OAAO,aAAa,GAAG;gBACvB,OAAO,SAAS,GAAG;gBACnB,OAAO,aAAa,GAAG;YACzB,OACK,IAAI,eAAe,UAAU;gBAChC,OAAO,aAAa,GAAG;gBACvB,IAAI,OAAO,gBAAgB,EAAE;oBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;oBAChC,+DAA+D;oBAC9D,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;gBAC9D;YACF,OAAO;gBACL,OAAO,aAAa,GAAG;gBACvB,4BAA4B;gBAC5B,IAAI,OAAO,gBAAgB,EAAE;oBAC1B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;gBAC9D;YACF;QACF;2BAAG;QAAC;QAAQ;QAAY;QAAW;QAAY;QAAW;QAAa;KAAY;IAEnF,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YACrC,yCAAyC;YACzC,IAAI,SAAS;YACb,IAAI,QAAQ;YACZ,IAAI,QAAQ;YACZ,IAAI,QAA8B;YAElC,MAAM;qEAAc,CAAC;oBACnB,yDAAyD;oBACzD,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;oBAClD,IAAI,gBAAgB,YAAY,gBAAgB,WAAW,gBAAgB,YAAY,gBAAgB,QAAQ;oBAE/G,SAAS;oBACT,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;oBACrC,QAAQ,QAAQ,CAAC;oBACjB,QAAQ,QAAQ,CAAC;oBAEjB,IAAI,gBAAgB,aAAa;wBAC/B,OAAO,SAAS,GAAG;wBACnB,MAAM,OAAO,IAAI,kJAAA,CAAA,OAAW,CAAC;4BAC3B,MAAM;4BACN,KAAK;4BACL,OAAO;4BACP,QAAQ;4BACR,MAAM;4BACN,QAAQ;4BACR,iBAAiB;gCAAC;gCAAG;6BAAE;4BACvB,YAAY;wBACd;wBACA,iBAAiB;wBACjB,OAAO,GAAG,CAAC;wBACX,QAAQ,gCAAgC;oBAC1C;oBAEA,IAAI,gBAAgB,aAAa;wBAC/B,QAAQ,IAAI,kJAAA,CAAA,OAAW,CAAC;4BACtB,MAAM;4BACN,KAAK;4BACL,OAAO;4BACP,QAAQ;4BACR,MAAM,AAAC,OAAe,SAAS,IAAI;4BACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;4BACvC,aAAa,AAAC,OAAe,WAAW,IAAI;4BAC5C,YAAY;wBACd;oBACF,OAAO,IAAI,gBAAgB,UAAU;wBACnC,QAAQ,IAAI,kJAAA,CAAA,SAAa,CAAC;4BACxB,MAAM;4BACN,KAAK;4BACL,QAAQ;4BACR,MAAM,AAAC,OAAe,SAAS,IAAI;4BACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;4BACvC,aAAa,AAAC,OAAe,WAAW,IAAI;4BAC5C,YAAY;wBACd;oBACF;oBAEA,IAAI,OAAO;wBACT,OAAO,GAAG,CAAC;oBACb;gBACF;;YAEA,MAAM;qEAAc,CAAC;oBACnB,IAAI,CAAC,QAAQ;oBAEb,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;oBACrC,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;oBAElD,IAAI,gBAAgB,eAAe,eAAe;wBAChD,MAAM,OAAO;wBACb,KAAK,GAAG,CAAC;4BACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;4BAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;wBAC/B;wBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;4BACrB,KAAK,GAAG,CAAC;gCAAE,MAAM,QAAQ,CAAC;4BAAC;wBAC7B;wBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;4BACrB,KAAK,GAAG,CAAC;gCAAE,KAAK,QAAQ,CAAC;4BAAC;wBAC5B;oBACF,OAAO,IAAI,gBAAgB,eAAe,OAAO;wBAC/C,MAAM,OAAO;wBACb,KAAK,GAAG,CAAC;4BACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;4BAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;wBAC/B;wBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;4BACrB,KAAK,GAAG,CAAC;gCAAE,MAAM,QAAQ,CAAC;4BAAC;wBAC7B;wBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;4BACrB,KAAK,GAAG,CAAC;gCAAE,KAAK,QAAQ,CAAC;4BAAC;wBAC5B;oBACF,OAAO,IAAI,gBAAgB,UAAU;wBACnC,MAAM,SAAS;wBACf,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,MAAM;wBAC5F,OAAO,GAAG,CAAC;4BAAE;wBAAO;oBACtB;oBAEA,OAAO,SAAS;gBAClB;;YAEA,MAAM;mEAAY;oBAChB,SAAS;oBACT,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;oBAElD,IAAI,gBAAgB,eAAe,eAAe;wBAChD,gBAAgB;wBAChB,OAAO,MAAM,CAAC;wBACd,iBAAiB;wBACjB,OAAO,SAAS,GAAG,MAAM,sBAAsB;oBACjD;oBAEA,IAAI,OAAO;wBACT,MAAM,GAAG,CAAC;4BAAE,YAAY;wBAAK;wBAC7B,QAAQ;oBACV;oBACA,SAAS;gBACX;;YAEA,OAAO,EAAE,CAAC,cAAc;YACxB,OAAO,EAAE,CAAC,cAAc;YACxB,OAAO,EAAE,CAAC,YAAY;YAEtB;yDAAO;oBACL,OAAO,GAAG,CAAC,cAAc;oBACzB,OAAO,GAAG,CAAC,cAAc;oBACzB,OAAO,GAAG,CAAC,YAAY;gBACzB;;QACF;gDAAG;QAAC;QAAY;QAAW;QAAa;QAAa;KAAc;IAEnE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,EAAE;QAE5C,MAAM,UAAU,IAAI;QACpB,QAAQ,GAAG,GAAG,OAAO,SAAS,CAAC;YAC7B,MAAM,KAAK,IAAI;YACf,KAAK,KAAK,GAAG;YACb,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,YAAY;QACd;QAEA,QAAQ,MAAM,GAAG;YACf,OAAO,KAAK;YACZ,MAAM,QAAQ,IAAI,kJAAA,CAAA,cAAkB,CAAC;YACrC,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI;YAC9B,OAAO,SAAS,CAAC,KAAK,MAAM,IAAI;YAChC,OAAO,GAAG,CAAC;YACX,OAAO,SAAS;QAClB;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,UAAU,OAAO,IAAI,WAAW,KAAK,GAAG,KAAK,WAAW,MAAM,GAAG,GAAG;gBACtE,MAAM,eAAe,IAAI,kJAAA,CAAA,SAAa,CAAC,UAAU,OAAO,EAAE;oBACxD,OAAO,WAAW,KAAK;oBACvB,QAAQ,WAAW,MAAM;oBACzB,iBAAiB;gBACnB;gBAEA,oDAAoD;gBACpD,aAAa,SAAS,GAAG;gBAEzB,UAAU;gBAEV,sBAAsB;gBACtB;wCAAO;wBACL,aAAa,OAAO;oBACtB;;YACF;QACF;2BAAG;QAAC;QAAY;KAAU;IAE1B,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,QAAQ;gBACV,MAAM,UAAU,kBAAkB;gBAClC,OAAO;YACT;QACF;2BAAG;QAAC;QAAQ;KAAkB;IAE9B,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,WAAW;YAEhB,UAAU,gBAAgB,CAAC,SAAS,aAAa;gBAAE,SAAS;YAAM;YAClE,UAAU,gBAAgB,CAAC,aAAa;YACxC,UAAU,gBAAgB,CAAC,aAAa;YACxC,UAAU,gBAAgB,CAAC,WAAW;YAEtC;oCAAO;oBACL,UAAU,mBAAmB,CAAC,SAAS;oBACvC,UAAU,mBAAmB,CAAC,aAAa;oBAC3C,UAAU,mBAAmB,CAAC,aAAa;oBAC3C,UAAU,mBAAmB,CAAC,WAAW;gBAC3C;;QACF;2BAAG;QAAC;QAAa;QAAiB;QAAiB;KAAc;IAEjE,MAAM,YAAY;QAChB,IAAI,WAAW,OAAO;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,YAAa;YAKb,QAAQ;QACV;;0BAGA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAkB;oBAIlB,gBAAiB;oBACjB,oBAAoB,AAAC,GAAY,OAAV,MAAK,OAAU,OAAL,MAAK;gBACxC;;;;;;0BAIF,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,SAAS;oBACT,OAAO,WAAW,KAAK;oBACvB,QAAQ,WAAW,MAAM;gBAC3B;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAO;;sCACN,6LAAC,+HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC;gCAAO,WAAU;;oCACf,KAAK,KAAK,CAAC,OAAO;oCAAK;;;;;;;;;;;;sCAG5B,6LAAC,+HAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAoD,MAAK;sCACjF,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDAAK,WAAU;;oDAAyB,KAAK,KAAK,CAAC,OAAO;oDAAK;;;;;;;;;;;;;kDAElE,6LAAC,8HAAA,CAAA,SAAM;wCACL,OAAO;4CAAC,OAAO;yCAAI;wCACnB,eAAe,CAAC;4CACd,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG;4CAC3B,QAAQ;4CACR,IAAI,QAAQ;gDACV,OAAO,OAAO,CAAC;gDACf,OAAO,SAAS;4CAClB;wCACF;wCACA,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAI;oDAAI;oDAAI;oDAAK;oDAAK;oDAAK;oDAAK;oDAAK;oDAAK;iDAAK,CAAC,GAAG,CAAC,CAAC,wBACrD,6LAAC;wDAEC,SAAS;4DACP,MAAM,UAAU,UAAU;4DAC1B,QAAQ;4DACR,IAAI,QAAQ;gEACV,OAAO,OAAO,CAAC;gEACf,OAAO,SAAS;4DAClB;wDACF;wDACA,WAAW,AAAC,+CAIX,OAHC,KAAK,KAAK,CAAC,OAAO,SAAS,UACvB,uCACA;;4DAGL;4DAAQ;;uDAfJ;;;;;;;;;;0DAqBX,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAtiBM;;QAuBA,kJAAA,CAAA,iBAAc;;;KAvBd;uCAwiBS", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-9 px-2 min-w-9\",\n        sm: \"h-8 px-1.5 min-w-8\",\n        lg: \"h-10 px-2.5 min-w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Toggle({\n  className,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <TogglePrimitive.Root\n      data-slot=\"toggle\"\n      className={cn(toggleVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Toggle, toggleVariants }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ijBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KAMqB;QANrB,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,GAAG,OAEgC,GANrB;IAOd,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useImageEditor, Tool } from './context';\r\nimport * as fabric from 'fabric';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Toggle } from '@/components/ui/toggle';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport {\r\n  MousePointer2,\r\n  Square,\r\n  Circle,\r\n  Type,\r\n  Brush,\r\n  Eraser,\r\n  Image,\r\n  Download,\r\n  Trash2,\r\n  Copy,\r\n  Layers,\r\n  Hand,\r\n  Move,\r\n  Crop\r\n} from 'lucide-react';\r\nimport React, { useRef, useState } from 'react';\r\n\r\nconst Toolbar = () => {\r\n  const {\r\n    canvas,\r\n    activeTool,\r\n    setActiveTool,\r\n    brushSize,\r\n    setBrushSize,\r\n    brushColor,\r\n    setBrushColor,\r\n    fillColor,\r\n    setFillColor,\r\n    strokeColor,\r\n    setStrokeColor,\r\n    strokeWidth,\r\n    setStrokeWidth,\r\n    toolbarPosition,\r\n    setToolbarPosition\r\n  } = useImageEditor();\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });\r\n\r\n  const tools: { id: Tool; icon: React.ReactNode; label: string }[] = [\r\n    { id: 'select', icon: <MousePointer2 size={18} />, label: 'Select' },\r\n    { id: 'hand', icon: <Hand size={18} />, label: 'Hand' },\r\n    { id: 'selection', icon: <Crop size={18} />, label: 'Select Area' },\r\n    { id: 'rectangle', icon: <Square size={18} />, label: 'Rectangle' },\r\n    { id: 'circle', icon: <Circle size={18} />, label: 'Circle' },\r\n    { id: 'text', icon: <Type size={18} />, label: 'Text' },\r\n    { id: 'brush', icon: <Brush size={18} />, label: 'Brush' },\r\n    { id: 'eraser', icon: <Eraser size={18} />, label: 'Eraser' },\r\n  ];\r\n\r\n  const handleToolSelect = (tool: Tool) => {\r\n    setActiveTool(tool);\r\n\r\n    if (tool === 'text' && canvas) {\r\n      const text = new fabric.IText('Click to edit', {\r\n        left: 100,\r\n        top: 100,\r\n        fontFamily: 'Arial',\r\n        fontSize: 20,\r\n        fill: fillColor,\r\n      });\r\n      canvas.add(text);\r\n      canvas.setActiveObject(text);\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file && canvas) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const imgUrl = e.target?.result as string;\r\n        fabric.FabricImage.fromURL(imgUrl).then((img: any) => {\r\n          img.set({\r\n            left: 50,\r\n            top: 50,\r\n            scaleX: 0.5,\r\n            scaleY: 0.5,\r\n          });\r\n          canvas.add(img);\r\n          canvas.renderAll();\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    if (canvas) {\r\n      const dataURL = canvas.toDataURL({\r\n        format: 'png',\r\n        quality: 1,\r\n        multiplier: 1,\r\n      });\r\n      const link = document.createElement('a');\r\n      link.download = 'design.png';\r\n      link.href = dataURL;\r\n      link.click();\r\n    }\r\n  };\r\n\r\n  const handleClear = () => {\r\n    if (canvas) {\r\n      canvas.clear();\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    if (canvas) {\r\n      const activeObject = canvas.getActiveObject();\r\n      if (activeObject) {\r\n        // Simple duplication by creating a new object with same properties\r\n        const objData = activeObject.toObject();\r\n        const cloned = new (activeObject.constructor as any)(objData);\r\n        cloned.set({\r\n          left: (activeObject.left || 0) + 10,\r\n          top: (activeObject.top || 0) + 10,\r\n        });\r\n        canvas.add(cloned);\r\n        canvas.setActiveObject(cloned);\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  const deleteSelected = () => {\r\n    if (canvas) {\r\n      const activeObject = canvas.getActiveObject();\r\n      if (activeObject) {\r\n        if (activeObject.type === 'activeSelection') {\r\n          (activeObject as fabric.ActiveSelection).forEachObject((obj) => {\r\n            canvas.remove(obj);\r\n          });\r\n        } else {\r\n          canvas.remove(activeObject);\r\n        }\r\n        canvas.discardActiveObject();\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle toolbar dragging\r\n  const handleMouseDown = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {\r\n      setIsDragging(true);\r\n      setDragOffset({\r\n        x: e.clientX - toolbarPosition.x,\r\n        y: e.clientY - toolbarPosition.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseMove = (e: MouseEvent) => {\r\n    if (isDragging) {\r\n      setToolbarPosition({\r\n        x: e.clientX - dragOffset.x,\r\n        y: e.clientY - dragOffset.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n  };\r\n\r\n  // Add global mouse event listeners for dragging\r\n  React.useEffect(() => {\r\n    if (isDragging) {\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n      document.addEventListener('mouseup', handleMouseUp);\r\n      return () => {\r\n        document.removeEventListener('mousemove', handleMouseMove);\r\n        document.removeEventListener('mouseup', handleMouseUp);\r\n      };\r\n    }\r\n  }, [isDragging, dragOffset]);\r\n\r\n  // Add global keydown event listener for deleting objects\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      if (e.key === 'Delete' || e.key === 'Backspace') {\r\n        deleteSelected();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [canvas]);\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div\r\n        className=\"fixed z-50 flex flex-wrap items-center gap-2 p-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg cursor-move select-none\"\r\n        style={{\r\n          left: toolbarPosition.x,\r\n          top: toolbarPosition.y,\r\n          maxWidth: '90vw'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n      >\r\n        {/* Drag Handle */}\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <div className=\"drag-handle flex items-center justify-center w-6 h-6 text-white/50 hover:text-white/80 cursor-grab active:cursor-grabbing\">\r\n              <Move size={14} />\r\n            </div>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Drag to move toolbar</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n        {/* Tool Selection */}\r\n        <div className=\"flex items-center flex-wrap gap-1 bg-white/5 rounded-lg p-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n          {tools.map((tool) => (\r\n            <Tooltip key={tool.id}>\r\n              <TooltipTrigger asChild>\r\n                <Toggle\r\n                  pressed={activeTool === tool.id}\r\n                  onPressedChange={() => handleToolSelect(tool.id)}\r\n                  className={`h-10 w-10 hover:bg-white/10 ${activeTool === tool.id ? 'bg-white/20 text-white' : ''}`}\r\n                  aria-label={tool.label}\r\n                >\r\n                  {tool.icon}\r\n                </Toggle>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{tool.label}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          ))}\r\n        </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Color Controls */}\r\n      <div className=\"flex flex-wrap items-center gap-2\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border border-white/30\"\r\n                    style={{ backgroundColor: fillColor }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Fill Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Fill Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={fillColor}\r\n                onChange={(e) => setFillColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border-2\"\r\n                    style={{ borderColor: strokeColor, backgroundColor: 'transparent' }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Stroke Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Stroke Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={strokeColor}\r\n                onChange={(e) => setStrokeColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Size Controls */}\r\n      {(activeTool === 'brush' || activeTool === 'eraser') && (\r\n        <>\r\n          <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n            <Brush size={16} className=\"text-white/70\" />\r\n            <Slider\r\n              value={[brushSize]}\r\n              onValueChange={(value) => setBrushSize(value[0])}\r\n              max={50}\r\n              min={1}\r\n              step={1}\r\n              className=\"flex-1\"\r\n            />\r\n            <span className=\"text-xs text-white/70 w-6\">{brushSize}</span>\r\n          </div>\r\n\r\n          {activeTool === 'brush' && (\r\n            <Popover>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <PopoverTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                    >\r\n                      <div\r\n                        className=\"w-full h-full rounded border border-white/30\"\r\n                        style={{ backgroundColor: brushColor }}\r\n                      />\r\n                    </Button>\r\n                  </PopoverTrigger>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Brush Color</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n              <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n                <div className=\"space-y-3\">\r\n                  <label className=\"text-sm font-medium text-white\">Brush Color</label>\r\n                  <input\r\n                    type=\"color\"\r\n                    value={brushColor}\r\n                    onChange={(e) => setBrushColor(e.target.value)}\r\n                    className=\"w-full h-10 rounded border-0\"\r\n                  />\r\n                </div>\r\n              </PopoverContent>\r\n            </Popover>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {(activeTool === 'rectangle' || activeTool === 'circle') && (\r\n        <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n          <div className=\"w-4 h-0.5 bg-white/70 rounded\" />\r\n          <Slider\r\n            value={[strokeWidth]}\r\n            onValueChange={(value) => setStrokeWidth(value[0])}\r\n            max={20}\r\n            min={0}\r\n            step={1}\r\n            className=\"flex-1\"\r\n          />\r\n          <span className=\"text-xs text-white/70 w-6\">{strokeWidth}</span>\r\n        </div>\r\n      )}\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex flex-wrap items-center gap-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => fileInputRef.current?.click()}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              {/* <Upload size={18} /> */}\r\n              <Image size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Upload Image</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleDownload}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Download size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Download Design</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopy}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Copy size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Duplicate Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={deleteSelected}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Trash2 size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Delete Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleClear}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-red-400 hover:text-red-300\"\r\n            >\r\n              <Layers size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Clear Canvas</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </div>\r\n    </div>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default Toolbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AA1BA;;;;;;;;;;;AA4BA,MAAM,UAAU;;IACd,MAAM,EACJ,MAAM,EACN,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,WAAW,EACX,cAAc,EACd,eAAe,EACf,kBAAkB,EACnB,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE1D,MAAM,QAA8D;QAClE;YAAE,IAAI;YAAU,oBAAM,6LAAC,+NAAA,CAAA,gBAAa;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QACnE;YAAE,IAAI;YAAQ,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAa,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAc;QAClE;YAAE,IAAI;YAAa,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAY;QAClE;YAAE,IAAI;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QAC5D;YAAE,IAAI;YAAQ,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAS,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAQ;QACzD;YAAE,IAAI;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;KAC7D;IAED,MAAM,mBAAmB,CAAC;QACxB,cAAc;QAEd,IAAI,SAAS,UAAU,QAAQ;YAC7B,MAAM,OAAO,IAAI,kJAAA,CAAA,QAAY,CAAC,iBAAiB;gBAC7C,MAAM;gBACN,KAAK;gBACL,YAAY;gBACZ,UAAU;gBACV,MAAM;YACR;YACA,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;YACZ;QAAb,MAAM,QAAO,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QACpC,IAAI,QAAQ,QAAQ;YAClB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;oBACA;gBAAf,MAAM,UAAS,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;gBAC/B,kJAAA,CAAA,cAAkB,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACvC,IAAI,GAAG,CAAC;wBACN,MAAM;wBACN,KAAK;wBACL,QAAQ;wBACR,QAAQ;oBACV;oBACA,OAAO,GAAG,CAAC;oBACX,OAAO,SAAS;gBAClB;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,UAAU,OAAO,SAAS,CAAC;gBAC/B,QAAQ;gBACR,SAAS;gBACT,YAAY;YACd;YACA,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG;YAChB,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,QAAQ;YACV,OAAO,KAAK;YACZ,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,MAAM,eAAe,OAAO,eAAe;YAC3C,IAAI,cAAc;gBAChB,mEAAmE;gBACnE,MAAM,UAAU,aAAa,QAAQ;gBACrC,MAAM,SAAS,IAAK,aAAa,WAAW,CAAS;gBACrD,OAAO,GAAG,CAAC;oBACT,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;oBACjC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;gBACjC;gBACA,OAAO,GAAG,CAAC;gBACX,OAAO,eAAe,CAAC;gBACvB,OAAO,SAAS;YAClB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,eAAe,OAAO,eAAe;YAC3C,IAAI,cAAc;gBAChB,IAAI,aAAa,IAAI,KAAK,mBAAmB;oBAC1C,aAAwC,aAAa,CAAC,CAAC;wBACtD,OAAO,MAAM,CAAC;oBAChB;gBACF,OAAO;oBACL,OAAO,MAAM,CAAC;gBAChB;gBACA,OAAO,mBAAmB;gBAC1B,OAAO,SAAS;YAClB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,AAAC,EAAE,MAAM,CAAiB,SAAS,CAAC,QAAQ,CAAC,gBAAgB;YAC/F,cAAc;YACd,cAAc;gBACZ,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;gBAChC,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;YAClC;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,mBAAmB;gBACjB,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;gBAC3B,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;YAC7B;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,gDAAgD;IAChD,6JAAA,CAAA,UAAK,CAAC,SAAS;6BAAC;YACd,IAAI,YAAY;gBACd,SAAS,gBAAgB,CAAC,aAAa;gBACvC,SAAS,gBAAgB,CAAC,WAAW;gBACrC;yCAAO;wBACL,SAAS,mBAAmB,CAAC,aAAa;wBAC1C,SAAS,mBAAmB,CAAC,WAAW;oBAC1C;;YACF;QACF;4BAAG;QAAC;QAAY;KAAW;IAE3B,yDAAyD;IACzD,6JAAA,CAAA,UAAK,CAAC,SAAS;6BAAC;YACd,MAAM;mDAAgB,CAAC;oBACrB,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,aAAa;wBAC/C;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;4BAAG;QAAC;KAAO;IAEX,qBACE,6LAAC,+HAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,MAAM,gBAAgB,CAAC;gBACvB,KAAK,gBAAgB,CAAC;gBACtB,UAAU;YACZ;YACA,aAAa;;8BAGb,6LAAC,+HAAA,CAAA,UAAO;;sCACN,6LAAC,+HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;sCAGhB,6LAAC,+HAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;8BAIP,6LAAC,iIAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;oBAA8D,aAAa,CAAC,IAAM,EAAE,eAAe;8BAC/G,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,eAAe,KAAK,EAAE;wCAC/B,iBAAiB,IAAM,iBAAiB,KAAK,EAAE;wCAC/C,WAAW,AAAC,+BAAqF,OAAvD,eAAe,KAAK,EAAE,GAAG,2BAA2B;wCAC9F,cAAY,KAAK,KAAK;kDAErB,KAAK,IAAI;;;;;;;;;;;8CAGd,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAG,KAAK,KAAK;;;;;;;;;;;;2BAZJ,KAAK,EAAE;;;;;;;;;;8BAkB3B,6LAAC,iIAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;oBAAoC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCACtF,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,UAAO;;sDACN,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAU;;;;;;;;;;;;;;;;;;;;;sDAK5C,6LAAC,+HAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,UAAO;;sDACN,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,aAAa;4DAAa,iBAAiB;wDAAc;;;;;;;;;;;;;;;;;;;;;sDAK1E,6LAAC,+HAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC,iIAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;gBAG3C,CAAC,eAAe,WAAW,eAAe,QAAQ,mBACjD;;sCACE,6LAAC;4BAAI,WAAU;4BAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;8CAC1F,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,6LAAC,8HAAA,CAAA,SAAM;oCACL,OAAO;wCAAC;qCAAU;oCAClB,eAAe,CAAC,QAAU,aAAa,KAAK,CAAC,EAAE;oCAC/C,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;wBAG9C,eAAe,yBACd,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,UAAO;;sDACN,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAW;;;;;;;;;;;;;;;;;;;;;sDAK7C,6LAAC,+HAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;gBASvB,CAAC,eAAe,eAAe,eAAe,QAAQ,mBACrD,6LAAC;oBAAI,WAAU;oBAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCAC1F,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,8HAAA,CAAA,SAAM;4BACL,OAAO;gCAAC;6BAAY;4BACpB,eAAe,CAAC,QAAU,eAAe,KAAK,CAAC,EAAE;4BACjD,KAAK;4BACL,KAAK;4BACL,MAAM;4BACN,WAAU;;;;;;sCAEZ,6LAAC;4BAAK,WAAU;sCAA6B;;;;;;;;;;;;8BAIjD,6LAAC,iIAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;oBAAoC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCACtF,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;sCAGZ,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;gDAAM;oDAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;wCAC1C,WAAU;kDAGV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGjB,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGpB,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGhB,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,6LAAC,+HAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA7cM;;QAiBA,kJAAA,CAAA,iBAAc;;;KAjBd;uCA+cS", "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ImageEditorProvider } from './context';\r\nimport Canvas from './Canvas';\r\nimport Toolbar from './Toolbar';\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ImageEditorProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ImageEditor = ({ children }: ImageEditorProps) => {\r\n  return <ImageEditorProvider>{children}</ImageEditorProvider>;\r\n};\r\n\r\nImageEditor.Canvas = Canvas;\r\nImageEditor.Toolbar = Toolbar;\r\n\r\nexport default ImageEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAoB;IACjD,qBAAO,6LAAC,kJAAA,CAAA,sBAAmB;kBAAE;;;;;;AAC/B;KAFM;AAIN,YAAY,MAAM,GAAG,iJAAA,CAAA,UAAM;AAC3B,YAAY,OAAO,GAAG,kJAAA,CAAA,UAAO;uCAEd", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/design/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ImageEditor from '../../../../components/design/ImageEditor';\r\n\r\nexport default function DesignPage() {\r\n  return (\r\n    <ImageEditor>\r\n      <ImageEditor.Canvas />\r\n      <ImageEditor.Toolbar />\r\n    </ImageEditor>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC,gJAAA,CAAA,UAAW;;0BACV,6LAAC,gJAAA,CAAA,UAAW,CAAC,MAAM;;;;;0BACnB,6LAAC,gJAAA,CAAA,UAAW,CAAC,OAAO;;;;;;;;;;;AAG1B;KAPwB", "debugId": null}}]}